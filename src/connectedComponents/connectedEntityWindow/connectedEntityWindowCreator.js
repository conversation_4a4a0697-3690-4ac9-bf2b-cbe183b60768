import { connect } from 'react-redux';
import { entityWindowFieldChanged, entityWindowFieldChangedContextually, entityWindowAutoCompleteInput, entityWindowDropdownVisibilityChange, entityWindowClose, entityWindowContextualEditStart, entityWindowContextualEditApply, entityWindowContextualEditCancel, entityWindowSetFieldErrors, entityWindowClearSuggestions, entityWindowSetFormError, entityWindowSetActiveTab, entityWindowExpandCollapseSection, entityWindowDisplayValueChangedContextually, entityWindowDisplayValueChanged, setBatchFields, entityWindowFieldChangedForAllEntities, revertUiChangesForAllEntities, entityWindowSetFieldErrorsForAllEntities, changeReadOnlyFieldsVisibility, addNewEntityAction, entityWindowOpenRequest, entityWindowCloseGlobalCreateModal, entityWindowDrivenFieldChanged } from '../../actions/entityWindowActions';
import { autocompleteSearch } from '../../actions/autocompleteActions';
import { getTableStructure, getFieldInfoSelector } from '../../selectors/tableStructureSelectors';
import { getEntityWindowAvatarConfig, entityWindowHasSection, getEntityWindowButtonLabelsSelector, createGetUiEntitySelector, getEntityWindowErrorSelector, getEntityDependantTitleSelector, getEntitySpecificProps, getCurrentDetailsPaneForPage, getModuleSpecificProps, getHasWindowInContextualEditSelector, getEntityOperationTitlePrefixSelector, getExternalTableDependantIdsSelector, getCustomTitleIconSelector, getEntityWindowSelectedTabKey, getEntityWindowFieldValueCaptionSelector, getFieldMessagesSelector } from '../../selectors/entityWindowSelectors';
import { EntityWindow, BatchEntityWindow, RoleListEntityWindow, RoleTemplatesListEntityWindow } from '../../lib/entityWindow';
import { getBulkEditTab, getDetailsTab, getHistoryTab, getRoleListTab, getRoleTemplatesListTab } from './tabs';
import { getRequestSelection } from '../../utils/autoCompleteUtils';
import { getEntityInfoSelector, getEntitySingularAliasSelector } from '../../selectors/entityStructureSelectors';
import { getEntityWindowFieldBlankValueSelector } from '../../selectors/blankValuesSelectors';
import { getFieldPrefixSelector, getFieldSuffixExtraInfoSelector, getFieldSuffixSelector } from '../../selectors/tableFieldsSelectors';
import { getErrorStaticMessagesSelector } from '../../selectors/errorSelectors';
import { withHotKeys } from '../../lib/hotKeys/hotKeys';
import { ENTITY_WINDOW_OPERATIONS, ENTITY_WINDOW_MODULES, ENTITY_WINDOW_TAB_KEYS, ENTITY_WINDOW_SECTION_TYPES, ENTITY_WINDOW_SECTION_KEYS, EDIT_ALL_ENTITY_ID, ENTITY_WINDOW_ACTION_GROUPS } from '../../constants/entityWindowConsts';
import { skillsDispatchToProps } from './skills/dispatchToProps';
import { loadAudit } from '../../actions/auditActions';
import { promptAction } from '../../actions/promptActions';
import { getFunctionalAccessSelector } from '../../selectors/applicationFnasSelectors';
import { CREATE_FNAS_PER_TABLENAME, DELETE_FNAS_PER_TABLENAME, EDIT_FNAS_PER_TABLENAME } from '../../constants/tablesConsts';
import { ROLE_INBOX_PAGE_ALIAS, ROLE_REQUEST_FORM, TABLE_NAMES } from '../../constants';
import store from '../../store/configureStore';
import { ConnectedEntityWindowCarousel } from './carousel/connectedEntityWindowCarousel';
import { getAccessibleEntitiesIdsSelector, getApplicationAccessSelector } from '../../selectors/userEntityAccessSelectors';
import { ENTITY_ACCESS_TYPES } from '../../constants/entityAccessConsts';
import { PLANNER_BOOKING_GROUPS_ALIAS, PLANNER_MASTER_REC_ALIAS, PLANNER_ROLEREQUESTS_ALIAS } from '../../constants/plannerConsts';
import { ENTITY_WINDOW } from '../../actions/actionTypes';
import { getEditAllErrors, getRevertValidationActionsForCustomComponent, isBudgetSectionField, isEditAllEntity } from '../../utils/entityStructureUtils';
import { ConnectedRolesItemsList } from '../roleGroupRequest/connectedRolesItemsList';
import { getRoleRequestGroupGuidSelector, getRoleRequestStatusGuidSelector } from '../../selectors/roleGroupDetailsPageSelectors';
import { ROLE_ITEM_STATUS_KEYS } from '../../constants/rolesConsts';
import { getCurrentPageAliasSelector, getNavPageTitleSelector, getPageStateParamsSelector } from '../../selectors/navigationSelectors';
import { batchActions } from 'redux-batched-actions';
import { shouldRenderRoleRequestField, shouldValidateField, shouldRenderJobField, dispatchAdditionalOnCloseActions } from '../../utils/entityWindowUtils';
import { ConnectedEntityWindowFooter } from './connectedEntityWindowFooter';
import { getComplexGroupedFieldsForTable, getFieldCaption } from '../../utils/fieldUtils';
import { getNormalRenderFieldOptionsBuilder } from './renderFieldOptionsBuilders/getNormalRenderFieldOptionsBuilder';
import { getCriteriaRenderFieldOptionsBuilder } from './renderFieldOptionsBuilders/getCriteriaRenderFieldOptionsBuilder';
import { setCriteriaFieldsError } from '../../actions/criteriaActions';
import { ConnectedEWMessageArea } from '../connectedMessageArea/connectedEWMessageArea';
import getSectionSpecificProps from './utils/getSectionSpecificProps';
import { getIsMultipleAssigneesEnabled } from '../../selectors/functionalityConfigurationSelectors';
import { getPageTableDatas } from '../../selectors/commonSelectors';
import * as actionTypes from '../../actions/actionTypes';
import { BOOKING_END, BOOKING_FIXEDTIME, BOOKING_JOB_GUID, BOOKING_RESOURCE_GUIDS, BOOKING_START, BOOKING_STATUS, BOOKING_TIME_ALLOCATION, RESOURCE_CMEBLUE, RESOURCE_CMEGREEN, RESOURCE_CMERED, RESOURCE_CMEYELLOW, ROLEREQUEST_FIELDS } from '../../constants/fieldConsts';
import { pushUrl } from '../../actions/navigateActions';
import { EDIT_REPEAT_BOOKING_TYPE, PREVIEW_ENTITY_KEY, REPEAT_BOOKING_OPERATION } from '../../constants/globalConsts';
import { MARKETPLACE_PAGE_ALIAS, PREVIEW_ENTITY_PAGE_ALIAS } from '../../constants/marketplacePageConsts';
import { ConnectedRoleTemplatesList } from '../connectedRoleTemplatesList';
import { getCollectionAliasSelectorCreator } from '../../selectors/collectionAliasSelector';
import { getTableDataRoleRequestStatusGuidSelector } from '../../selectors/roleRequestsSelector';
import { isRecentWorkGridHasData } from '../../utils/workHistoryUtils';
import { entityWindowWorkHistoryPagedDataSelector } from '../../selectors/workHistorySelector';
import { ConnectedRoleGroupListDetailsPaneHeader } from './header/connectedRoleGroupListDetailsPaneHeader';
import { getFieldAccessLevelSelector } from '../../selectors/entityWindowSectionsSelectors';
import { changeToNonRepeatBooking, createRepeatBooking, openWarningUpdateWindow, repeatBookingGetAllResourceAvailability, repeatBookingOpenDialog, repeatBookingUpdateEditType, updateRepeatBooking } from '../../actions/repeatBookingActions';
import { getEditingRepeatBookingAlertMessage, getRepeatBookingDialogDataSelector } from '../../selectors/repeatBookingSelectors';
import { getUpdatedRepeatBookingExplanation } from '../../utils/fieldControlUtils';
import { isEmptyObject, isNullOrUndefined } from '../../utils/commonUtils';
import { repeatEveryOptionValue } from '../../constants/repeatBookingConsts';
import { formatToUtcISOString, getStartOfDay } from '../../utils/dateUtils';
import { getSkillPreferenceSelector, getResourceSkillPreferenceSelector } from '../../selectors/resourceSkillsSelectors';
import { getFeatureFlagSelector } from '../../selectors/featureManagementSelectors';
import { clearFieldValueMessages, getBookingJobHoursOverbudget } from '../../actions/entityWindowDrivenActions';
import { boookingJobHoursOverBudgetMessage } from '../../utils/messages';
const {
    COMMENTS_SECTION_TYPE
} = ENTITY_WINDOW_SECTION_TYPES;

const {
    PLANNER_PAGE_MODAL,
    PLANNER_PAGE_BATCH_MODAL,
    PLANNER_PAGE_DETAILS_PANE,
    PLANNER_PAGE_BATCH_DETAILS_PANE,
    JOBS_PAGE_DETAILS_PANE,
    JOBS_PAGE_MODAL,
    JOBS_PAGE_BATCHED_DETAILS_PANE,
    JOBS_PAGE_BATCH_MODAL,
    JOBS_PAGE_ROLEGROUPLIST_BATCHED_DETAILS_PANE,
    JOBS_PAGE_RESOURCE_DETAILS_PANE,
    RESOURCES_PAGE_DETAILS_PANE,
    RESOURCES_PAGE_MODAL,
    RESOURCES_PAGE_BATCHED_DETAILS_PANE,
    RESOURCES_PAGE_BATCH_MODAL,
    NOTIFICATION_PAGE_MODAL,
    ROLE_INBOX_PAGE_MODAL,
    ROLE_INBOX_PAGE_DETAILS_PANE,
    ROLE_INBOX_PAGE_BATCH_DETAILS_PANE,
    ROLE_INBOX_PAGE_BATCH_MODAL,
    MANAGE_ROLE_TEMPLATES_MODAL,
    JOBS_PAGE_ROLEGROUPLIST_DETAILS_PANE
} = ENTITY_WINDOW_MODULES;

const {
    BOOKING,
    JOB,
    RESOURCE,
    ROLEREQUEST
} = TABLE_NAMES;

const getEmptyOption = (tableName, fieldName) => {
    return { id: undefined, value: undefined };
};

const modulesWithHistoryTab = [
    PLANNER_PAGE_MODAL,
    PLANNER_PAGE_BATCH_DETAILS_PANE,
    PLANNER_PAGE_DETAILS_PANE,
    PLANNER_PAGE_BATCH_MODAL,
    NOTIFICATION_PAGE_MODAL,
    JOBS_PAGE_DETAILS_PANE,
    JOBS_PAGE_MODAL,
    JOBS_PAGE_BATCH_MODAL,
    JOBS_PAGE_BATCHED_DETAILS_PANE,
    JOBS_PAGE_RESOURCE_DETAILS_PANE,
    RESOURCES_PAGE_DETAILS_PANE,
    RESOURCES_PAGE_MODAL,
    RESOURCES_PAGE_BATCH_MODAL,
    RESOURCES_PAGE_BATCHED_DETAILS_PANE,
    ROLE_INBOX_PAGE_MODAL,
    ROLE_INBOX_PAGE_DETAILS_PANE,
    ROLE_INBOX_PAGE_BATCH_MODAL,
    ROLE_INBOX_PAGE_BATCH_DETAILS_PANE
];

const tableNamesWithHistoryTab = [BOOKING, JOB, RESOURCE, ROLEREQUEST];

const shouldShowHistoryTab = (tableName, moduleName, operation) => {
    return operation !== ENTITY_WINDOW_OPERATIONS.CREATE && tableNamesWithHistoryTab.includes(tableName) && modulesWithHistoryTab.includes(moduleName);
};

export const shouldRenderField = ({ entity, tableName, operation, moduleName, getLinkedData, uiEntity }, field, isEnabledBudgetHoursAndRevenue = false) => {
    let shouldRender = true;

    switch (tableName) {
        case TABLE_NAMES.ROLEREQUEST: {
            shouldRender = shouldRenderRoleRequestField(entity, field, operation, moduleName);
            break;
        }
        case TABLE_NAMES.JOB: {
            shouldRender = shouldRenderJobField(uiEntity, field, getLinkedData, isEnabledBudgetHoursAndRevenue);
            break;
        }
    }

    return shouldRender;
};

const moduleImplementingComponents = {
    [PLANNER_PAGE_BATCH_MODAL]: BatchEntityWindow,
    [PLANNER_PAGE_BATCH_DETAILS_PANE]: BatchEntityWindow,
    [JOBS_PAGE_BATCHED_DETAILS_PANE]: BatchEntityWindow,
    [JOBS_PAGE_BATCH_MODAL]: BatchEntityWindow,
    [JOBS_PAGE_ROLEGROUPLIST_BATCHED_DETAILS_PANE]: BatchEntityWindow,
    [RESOURCES_PAGE_BATCHED_DETAILS_PANE]: BatchEntityWindow,
    [RESOURCES_PAGE_BATCH_MODAL]: BatchEntityWindow,
    [ROLE_REQUEST_FORM]: RoleListEntityWindow,
    [ROLE_INBOX_PAGE_BATCH_DETAILS_PANE]: BatchEntityWindow,
    [ROLE_INBOX_PAGE_BATCH_MODAL]: BatchEntityWindow,
    [MANAGE_ROLE_TEMPLATES_MODAL]: RoleTemplatesListEntityWindow
};

const moduleImplementingHeaderComponents = {
    [JOBS_PAGE_ROLEGROUPLIST_DETAILS_PANE]: ConnectedRoleGroupListDetailsPaneHeader
};

const getEntityFooter = () => {
    return ConnectedEntityWindowFooter;
};

const getEntityHeader = (moduleName) => moduleImplementingHeaderComponents[moduleName];

const getModuleImplementingComponent = (moduleName) => {
    return moduleImplementingComponents[moduleName] || EntityWindow;
};

const getTextByKey = (autoComplete, guid) => {
    const { suggestions } = autoComplete;
    let result;

    suggestions && suggestions.forEach(suggestion => {
        if (guid && guid.indexOf(suggestion.id) > -1) {
            result = suggestion.value;
        }
    });

    return result || guid;
};

const getEntityWindowCarouselComponent = () => {
    return ConnectedEntityWindowCarousel;
};

const getEntityWindowListComponent = () => {
    return ConnectedRolesItemsList;
};

const getEntityWindowRoleTemplatesListComponent = () => ConnectedRoleTemplatesList;

const handleEditAllSelectedSectionKeys = (sections, selectedEditAllFieldsKeys) => {
    let result = selectedEditAllFieldsKeys;

    (sections || []).forEach((section) => {
        if (result.includes(section.key)) {
            let shouldIncludeSectionKey = true;
            let i = 0;

            while (shouldIncludeSectionKey && i < section.fields.length) {
                if (!result.includes(section.fields[i].name)) {
                    shouldIncludeSectionKey = false;
                }
                i++;
            }

            if (!shouldIncludeSectionKey) {
                result.splice(result.indexOf(section.key), 1);
            }
        }
    });

    return result;
};

const handleEditAllGroupedFieldsSelection = (fieldName, selectedEditAllFieldsKeys, tableName) => {
    let result = selectedEditAllFieldsKeys;
    const complexGroupedFields = getComplexGroupedFieldsForTable(tableName);

    if (complexGroupedFields.includes(fieldName)) {
        result = result.filter(key => !complexGroupedFields.includes(key));
    }

    return result;
};

const getIsReadModeOnly = (window, canEdit, canCreate) => {
    const { operation } = window;

    return window.isReadModeOnly
        || (operation === ENTITY_WINDOW_OPERATIONS.EDIT && !canEdit)
        || (operation === ENTITY_WINDOW_OPERATIONS.CONTEXTUAL_EDIT && !canEdit)
        || (operation === ENTITY_WINDOW_OPERATIONS.CREATE && !canCreate);
};

const getRevertActionToDispatch = (field, props) => {
    const { moduleName, tableName, getFieldInfo, batchIds } = props;
    const { name: fieldName, multifield } = field;

    const fieldInfo = getFieldInfo(tableName, fieldName);

    let result = revertUiChangesForAllEntities(moduleName, fieldInfo, batchIds, getFieldInfo, tableName);

    if (multifield) {
        result = revertUiChangesForAllEntities(moduleName, field, batchIds, getFieldInfo, tableName);
    }

    return result;
};

const getContext = (action, payload, state) => {
    let result;

    switch (action) {
        case `${ENTITY_WINDOW.SUBMUT_BATCH_DELETE_ENTITIES}_${PLANNER_ROLEREQUESTS_ALIAS}`:
        case `${ENTITY_WINDOW.SUBMUT_BATCH_DELETE_ENTITIES}_${PLANNER_BOOKING_GROUPS_ALIAS}`:
        case `${ENTITY_WINDOW.SUBMUT_BATCH_DELETE_ENTITIES}_${TABLE_NAMES.ROLEREQUEST}`: {
            let { tableDataEntryGuids, tableName } = payload;
            const accessibleIds = getAccessibleEntitiesIdsSelector(state)(tableName, tableDataEntryGuids, ENTITY_ACCESS_TYPES.DELETE, DELETE_FNAS_PER_TABLENAME[tableName]);

            if (accessibleIds) {
                tableDataEntryGuids = tableDataEntryGuids.filter(id => accessibleIds.includes(id));
            }

            result = { tableDataEntryGuids };
            break;
        }
        case `${actionTypes.MOVE_PENDING_ROLEREQUEST_TIME_ALLOCATION}_${ROLE_INBOX_PAGE_ALIAS}`:
        case `${actionTypes.REMOVE_PENDING_ROLEREQUEST_TIME_ALLOCATION}_${ROLE_INBOX_PAGE_ALIAS}`: {
            const { entity } = payload;

            result = {
                description: entity[ROLEREQUEST_FIELDS.DESCRIPTION] || '',
                fixedTimeValue: entity[ROLEREQUEST_FIELDS.FIXEDTIME]
            };

            break;
        }
        default:
            result = {};
    }

    return result;
};

const getMessageAreaComponent = () => {
    return ConnectedEWMessageArea;
};

const getTitleButtonActionByKey = (key, propsFromState, propsFromDispatch) => {
    const { entity } = propsFromState;

    switch (key) {
        case PREVIEW_ENTITY_KEY: {
            const newParams = {
                subPageOption: {
                    subPageNavLink: 'rolepreview',
                    subPagesParams: {
                        roleSurrogateId: entity.rolerequest_surrogate_id
                    }
                }
            };

            const actionToDispatch = () => pushUrl(newParams, PREVIEW_ENTITY_PAGE_ALIAS);

            return (event) => propsFromDispatch.onHeaderButtonClick(event, actionToDispatch);
        }
        default:
            return () => () => {};

    }
};

const bookingSeriesExists = (bookings) => {
    if (isNullOrUndefined(bookings) || isEmptyObject(bookings)) return false;

    return Object.values(bookings).some(booking => {
        const entity = booking.entity;

        return entity && !isNullOrUndefined(entity.booking_bookingseries_guid);
    });
};

const staticEmptyArray = [];

const createEntityWindow = (
    moduleName,
    selectorInstance,
    pageKey,
    getDataCollections,
    getDataSelector = () => () => null,
    getUiEntity = (_tableName, uiEntity) => uiEntity,
    hotKeysProps = null
) => {
    const {
        getActiveWindowSelector,
        getEntityWindowActionsSelector,
        getEntityWindowSectionsSelector,
        getEntityWindowFormMessagesSelector,
        getLinkedDataWrappedSelector,
        getEntityWindowTitleSelector,
        getFieldValueExplanationSelector,
        getActionableFieldValueExplanationSelector,
        getCommonSectionStaticMessages,
        getEntityWindowDynamicConfigSelector,
        getSelectedEditAllFieldsKeysSelector,
        getMultiValueLinkedFieldListItemWrappedSelector,
        getIsInAutoCompleteStateSelector,
        getSuggestionsSelector,
        getLastUpdatedSelector,
        entityWindowAutoCompleteGetAdditionalFieldsSelector,
        shouldRenderSectionSelector,
        getActionBarItemsSelector,
        getExpandedActionPropsSelector,
        getActionAvailableIdsFromEWStateSelector,
        getActionAvailableIdsFromAccessSelector,
        getIntersectMapOfActionArraysSelector
    } = selectorInstance.getSelectors();
    const getUiEntitySelector = createGetUiEntitySelector(getUiEntity);

    const stateToProps = (state) => {
        const { entityWindow, resourceSkills, skillStructure, roleRequests, jobRoleGroupList, roleRequirementsState, plannerPage } = state;
        const { autoComplete } = entityWindow;
        const window = getActiveWindowSelector({
            entityWindow: entityWindow.window[moduleName]
        });
        const { tableName, entityId, entity, batchIds = staticEmptyArray, windows, hideFooter } = window;
        const detailsPane = getCurrentDetailsPaneForPage(state) || {};
        const pageAlias = pageKey || getCurrentPageAliasSelector(state);
        const { editType: repeatBookingEditType, operation: repeatBookingOperation, bookingSeries: bookingSeriesInformation, formData } = getRepeatBookingDialogDataSelector(state);
        const { repeatIntervalNumber, repeatInterval, repeatUntil } = formData;
        const editRepeatBookingType = repeatBookingOperation && repeatBookingEditType ? repeatBookingEditType : '';

        const repeatBookingExplanationData = {
            repeatIntervalNumber,
            repeatInterval,
            repeatUntil: repeatUntil ? formatToUtcISOString(getStartOfDay(repeatUntil)) : ''
        };

        const repeatBookingExplanation = getUpdatedRepeatBookingExplanation(state, repeatBookingExplanationData, entity.booking_start);
        const getAccessibleEntitiesIds = getAccessibleEntitiesIdsSelector(state);
        const getExternalTableDependantIds = getExternalTableDependantIdsSelector(state);
        const actions = getEntityWindowActionsSelector({
            entityWindow: entityWindow.window[moduleName],
            actionSettings: entityWindow.settings[moduleName].actions,
            applicationSettings: state.applicationSettings,
            tableDatas: getPageTableDatas(state, pageAlias),
            pageParams: getPageStateParamsSelector(state)(pageAlias),
            page: getCurrentPageAliasSelector(state),
            rootState: state,
            getAccessibleEntitiesIds,
            detailsPane,
            dataCollections: getDataCollections(state),
            roleRequirementsState,
            getExternalTableDependantIds
        });

        const getFieldAccessLevel = getFieldAccessLevelSelector(state);
        const getFeatureFlag = (flag) => getFeatureFlagSelector(flag)(state);

        const sectionsToHide = [ENTITY_WINDOW_SECTION_KEYS.BUDGET, ENTITY_WINDOW_SECTION_KEYS.CRITERIA_BUDGET];
        const sections = getEntityWindowSectionsSelector({
            entityWindow: window,
            sections: entityWindow.settings[moduleName].sections,
            resourceSkills,
            skillStructure,
            jobRoleGroupList,
            applicationSettings: state.applicationSettings,
            internationalization: state.internationalization,
            moduleName,
            roleRequests,
            adminSetting: state.adminSetting,
            sectionsToHide,
            [MARKETPLACE_PAGE_ALIAS]: state[MARKETPLACE_PAGE_ALIAS],
            plannerPage,
            getFeatureFlag
        });

        const formMessages = getEntityWindowFormMessagesSelector({
            ...window
        });

        const editingRepeatBookingAlertMessage = getEditingRepeatBookingAlertMessage(state);

        const getLinkedData = getLinkedDataWrappedSelector({
            tableStructure: getTableStructure(state),
            primaryTableName: tableName,
            autoComplete: autoComplete[moduleName],
            dataCollections: getDataCollections(state)
        });

        const getHasFunctionalAccessWrapped = getFunctionalAccessSelector(state);

        const getEntityInfo = getEntityInfoSelector(state);
        const getEntityDependantTitle = getEntityDependantTitleSelector(state);
        const getEntityOperationTitlePrefix = getEntityOperationTitlePrefixSelector(state);

        const getNavPageTitle = getNavPageTitleSelector(state);

        const titleProps = getEntityWindowTitleSelector(
            {
                entityWindow: entityWindow.window[moduleName],
                settings: entityWindow.settings
            },
            getEntityInfo,
            getEntityOperationTitlePrefix,
            getEntityDependantTitle,
            getNavPageTitle
        );

        const getFieldValueExplanation = getFieldValueExplanationSelector({
            fieldValueExplanations: entityWindow.fieldValueExplanations[moduleName],
            tableName,
            applicationSettings: state.applicationSettings
        });

        const getActionableFieldValueExplanation = getActionableFieldValueExplanationSelector({
            fieldValueExplanations: entityWindow.fieldValueExplanations[moduleName],
            tableName,
            applicationSettings: state.applicationSettings
        });

        const errorMessages = getErrorStaticMessagesSelector(state);
        const staticLabels = getCommonSectionStaticMessages(state, 'common');
        const entityWindowStaticMessages = getEntityWindowButtonLabelsSelector(state);

        const getData = getDataSelector(state);
        const getFieldPrefixWrapped = getFieldPrefixSelector(state);
        const getFieldSuffixWrapped = getFieldSuffixSelector(state);
        const getFieldSuffixExtraInfoWrapped = getFieldSuffixExtraInfoSelector(state);
        const getTableAliasWrapped = getEntitySingularAliasSelector(state);
        const getFieldBlankValue = getEntityWindowFieldBlankValueSelector(state);
        const getEntityWindowError = getEntityWindowErrorSelector(state);

        const getFieldInfo = getFieldInfoSelector(state);
        const dynamicConfig = getEntityWindowDynamicConfigSelector(state, moduleName);

        const customTitleIcon = getCustomTitleIconSelector(entityWindow.window[moduleName], getFieldInfo, getData);

        const getMultiValueLinkedFieldListItem = getMultiValueLinkedFieldListItemWrappedSelector({
            tableStructure: getTableStructure(state),
            dataCollections: getDataCollections(state),
            primaryTableName: tableName
        });

        const canEdit = getApplicationAccessSelector(state)(tableName, [entityId], ENTITY_ACCESS_TYPES.EDIT, EDIT_FNAS_PER_TABLENAME[tableName]);
        const canCreate = getApplicationAccessSelector(state)(tableName, [entityId], ENTITY_ACCESS_TYPES.CREATE, CREATE_FNAS_PER_TABLENAME[tableName]);

        const selectedEditAllFieldsKeys = getSelectedEditAllFieldsKeysSelector(state)(moduleName) || staticEmptyArray;
        const hasCommentsSection = entityWindowHasSection(state, tableName, COMMENTS_SECTION_TYPE, moduleName);
        const uiEntity = getUiEntitySelector(window, getData, state);
        const wrappedRecentWorkHistory = entityWindowWorkHistoryPagedDataSelector(state);
        const recentWorkHistoryData = wrappedRecentWorkHistory(moduleName);
        const hasRecentWorkHistory = isRecentWorkGridHasData(recentWorkHistoryData);

        const selectedItemsCount = batchIds.length;
        const idsByActionFromAccess = getActionAvailableIdsFromAccessSelector({ tableName, batchIds, getAccessibleEntitiesIds });
        const idsByActionFromEWState = getActionAvailableIdsFromEWStateSelector({ tableName, entityWindow: entityWindow.window[moduleName], batchIds });
        const idsByAction = getIntersectMapOfActionArraysSelector({ idsByActionFromAccess, idsByActionFromEWState });

        const expandedActionProps = getExpandedActionPropsSelector({ entityWindowStaticMessages, actions, selectedItemsCount, idsByAction, entityWindow: window, getExternalTableDependantIds, getAccessibleEntitiesIds });

        const actionBarItems = getActionBarItemsSelector({ actions: expandedActionProps, moduleName, staticLabels, actionsGroupMap: ENTITY_WINDOW_ACTION_GROUPS });
        const isReadModeOnly = getIsReadModeOnly(window, canEdit, canCreate);

        const footer = getEntityFooter();
        const header = getEntityHeader(moduleName);

        const entitySpecificProps = getEntitySpecificProps({ state, entity, entityId, tableName, staticLabels, getEntityInfo, entityWindow: window });
        const shouldRenderSectionWrapper = shouldRenderSectionSelector(state);

        const sectionSpecificProps = getSectionSpecificProps({ state, selectorInstance, sections, moduleName, entityId, uiEntity });

        const useMultipleAssignees = getIsMultipleAssigneesEnabled(state);
        const moduleSpecificProps = getModuleSpecificProps(state)(moduleName, entity);

        const activeTab = getEntityWindowSelectedTabKey(state)(moduleName);
        const { resourceSuggestions } = getRepeatBookingDialogDataSelector(state);
        const getResourceSkillPreference = getResourceSkillPreferenceSelector(state);
        const getEntityWindowFieldValueCaption = getEntityWindowFieldValueCaptionSelector(state);

        return {
            ...window,
            footer,
            header,
            moduleName,
            batchIds,
            isReadModeOnly,
            autoComplete: autoComplete[moduleName],
            isInAutoCompleteState: getIsInAutoCompleteStateSelector(autoComplete[moduleName]),
            getFieldInfo,
            getLinkedData,
            suggestions: getSuggestionsSelector(autoComplete[moduleName]),
            ...titleProps,
            titleIcon: customTitleIcon || titleProps.titleIcon,
            lastUpdated: getLastUpdatedSelector(window, getLinkedData),
            batchFields: entityWindow.settings[moduleName].batchFields,
            EWSettings: entityWindow.settings[moduleName],
            entity,
            entityId,
            uiEntity,
            getEmptyOption,
            sections,
            selectedEditAllFieldsKeys,
            messages: formMessages,
            actions: actionBarItems,
            avatarConfig: getEntityWindowAvatarConfig(state),
            getEntityInfo,
            getData,
            error: getEntityWindowError(moduleName),
            getFieldValueExplanation,
            getActionableFieldValueExplanation,
            getFieldPrefix: getFieldPrefixWrapped,
            getFieldSuffix: getFieldSuffixWrapped,
            getFieldSuffixExtraInfo: getFieldSuffixExtraInfoWrapped,
            getTableAlias: getTableAliasWrapped,
            getFieldBlankValue,
            shouldRenderSection: shouldRenderSectionWrapper,
            shouldRenderField,
            shouldValidateField,
            getComplexGroupedFieldsForTable,
            getMultiValueLinkedFieldListItem,
            dynamicConfig,
            errorMessages,
            staticLabels,
            ...sectionSpecificProps,
            hasCommentsSection,
            selectedItemsCount,
            getHasFunctionalAccess: getHasFunctionalAccessWrapped,
            idsByAction,
            getMessageAreaComponent,
            entityWindowStaticMessages,
            useMultipleAssignees,
            ...entitySpecificProps,
            hasRecentWorkHistory,
            ...moduleSpecificProps,
            hideFooter,
            activeTab,
            getFieldAccessLevel,
            editingRepeatBookingAlertMessage,
            resourceSuggestions,
            repeatBookingExplanation,
            editRepeatBookingType,
            bookingSeriesExists: bookingSeriesExists(windows),
            bookingSeriesInformation,
            skillPreferences: getSkillPreferenceSelector(state)(),
            getResourceSkillPreference,
            getFeatureFlag,
            getFieldValueCaption: getEntityWindowFieldValueCaption,
            getFieldCaption,
            getFieldMessages: getFieldMessagesSelector(state)
        };
    };

    const dispatchToProps = (dispatch, ownProps = {}) => {
        return {
            onFieldError: (errors, entityId) => {
                dispatch(entityWindowSetFieldErrors(moduleName, errors || {}, entityId));
            },
            onEntityFieldChange: (fieldInfo, fieldValue, entityId) => {
                const state = store.getState();
                const propsFromState = stateToProps(state);
                const { uiEntity, entityId: activeEntityId, batchIds, operation, tableName, entity } = propsFromState;
                const currentEntityId = entityId || activeEntityId;
                const isContextuallyEditing = (uiEntity[fieldInfo.name] || {}).isContextuallyEditing || false;

                if (isContextuallyEditing) {
                    dispatch(entityWindowFieldChangedContextually(moduleName, fieldInfo, fieldValue, currentEntityId, tableName));
                } else if (isEditAllEntity(currentEntityId)) {
                    const errors = {
                        [fieldInfo.name]: {
                            errors: []
                        }
                    };

                    dispatch(entityWindowFieldChangedForAllEntities(moduleName, fieldInfo, fieldValue, batchIds));
                    dispatch(entityWindowSetFieldErrorsForAllEntities(moduleName, batchIds, errors || {}));

                    if (isBudgetSectionField(fieldInfo.name)) {
                        dispatch(changeReadOnlyFieldsVisibility(moduleName, ENTITY_WINDOW_SECTION_KEYS.BUDGET, tableName, true));
                    }
                } else {
                    dispatch(entityWindowFieldChanged(moduleName, fieldInfo, fieldValue, currentEntityId, tableName));
                }

                if (tableName === TABLE_NAMES.BOOKING) {
                    const payload = {
                        operation,
                        tableName,
                        bookingStart: entity[BOOKING_START],
                        bookingEnd: entity[BOOKING_END],
                        bookingResourceGuids: entity[BOOKING_RESOURCE_GUIDS]
                    };
                    if (fieldInfo.name === BOOKING_START && entity[BOOKING_END]) {
                        payload['bookingStart'] = fieldValue;
                        payload['bookingEnd'] = entity[BOOKING_END];
                    } else if (fieldInfo.name === BOOKING_END && entity[BOOKING_START]) {
                        payload['bookingStart'] = entity[BOOKING_START];
                        payload['bookingEnd'] = fieldValue;
                    } else if (fieldInfo.name === BOOKING_RESOURCE_GUIDS) {
                        payload['bookingResourceGuids'] = fieldValue;
                    }
                    dispatch(repeatBookingGetAllResourceAvailability(payload));

                    if (fieldInfo.name === BOOKING_JOB_GUID
                        || fieldInfo.name === BOOKING_STATUS
                        || fieldInfo.name === BOOKING_START
                        || fieldInfo.name === BOOKING_END
                        || fieldInfo.name === BOOKING_FIXEDTIME) {
                        const jobHoursOverbudgetPayload = { ...entity };

                        if (fieldInfo.name === BOOKING_JOB_GUID) {
                            jobHoursOverbudgetPayload[BOOKING_JOB_GUID] = fieldValue.id;
                        }
                        if (fieldInfo.name === BOOKING_STATUS) {
                            jobHoursOverbudgetPayload[BOOKING_STATUS] = fieldValue.id;
                        }
                        dispatch(getBookingJobHoursOverbudget({ tableName, moduleName, entity: jobHoursOverbudgetPayload }));
                    }
                }
            },
            onAutoCompleteDropdownVisibilityChange: (tableName, fieldName, dropdownVisible, clearEnteredText = true) => {
                dispatch(entityWindowDropdownVisibilityChange(moduleName, tableName, fieldName, dropdownVisible, clearEnteredText));
                dispatch(entityWindowClearSuggestions(moduleName, fieldName));
            },
            onAutoCompleteInput: (tableName, fieldName, enteredText) => {
                dispatch(entityWindowAutoCompleteInput(moduleName, tableName, fieldName, enteredText));
                dispatch(entityWindowDropdownVisibilityChange(moduleName, tableName, fieldName, true, false));
            },
            onFieldDisplayValueChange: (fieldInfo, key) => {
                const state = store.getState();
                const propsFromState = stateToProps(state);
                const { uiEntity, autoComplete, entityId } = propsFromState;
                const isContextuallyEditing = (uiEntity[fieldInfo.name] || {}).isContextuallyEditing || false;
                //func getTextByKey is a hotfix for release v1.5
                //proper solution would be refactoring of fieldAutoComplete
                const enteredText = autoComplete && autoComplete[fieldInfo.name] ? getTextByKey(autoComplete[fieldInfo.name], key) : key;

                if (isContextuallyEditing) {
                    dispatch(entityWindowDisplayValueChangedContextually(moduleName, fieldInfo, enteredText, entityId));
                } else {
                    dispatch(entityWindowDisplayValueChanged(moduleName, fieldInfo, enteredText, entityId));
                }
            },
            onAutoCompleteSearchSuggest: (tableName, fieldInfo, value, filter, additionalSuggestions = []) => {
                const state = store.getState();
                const propsFromState = stateToProps(state);
                const { entity } = propsFromState;
                const buildAdditionalEntityLookupFields = entityWindowAutoCompleteGetAdditionalFieldsSelector(state, entity);
                const additionalFields = buildAdditionalEntityLookupFields((fieldInfo));

                const selection = getRequestSelection(tableName, fieldInfo, value, additionalFields, filter);
                dispatch(autocompleteSearch(moduleName, null, tableName, fieldInfo, selection, additionalSuggestions));
            },
            onInputBlur: (fieldName) => {
                dispatch(entityWindowSetFormError(moduleName, fieldName));
            },
            onAction: (action) => {
                const { type, collectionAliased, moduleAliased, payloadConfig = [] } = action;
                const state = store.getState();
                const propsFromState = stateToProps(state);

                const { idsByAction } = propsFromState;

                const availableData = { ...propsFromState, ...ownProps, ...idsByAction };

                const payloadData = payloadConfig.reduce((accumulator, currentAliasSetting) => {
                    const { key, alias, value } = currentAliasSetting;

                    accumulator[alias] = value !== undefined ? value : availableData[key];

                    return accumulator;
                }, {});

                let actionType = type;

                if (moduleAliased) {
                    actionType = `${actionType}_${moduleName}`;
                } else if (collectionAliased) {
                    actionType = `${actionType}_${availableData.collectionAlias}`;
                }

                const dispatchAction = {
                    type: actionType,
                    payload: {
                        ...payloadData,
                        tableNames: [availableData.tableName]
                    }
                };

                const context = getContext(actionType, payloadData, state);
                const pageAlias = pageKey || getCurrentPageAliasSelector(state);

                const { repeatBookingDialog } = state;

                if (dispatchAction.type === `${ENTITY_WINDOW.BATCH_SUBMIT_INSERT}_${PLANNER_BOOKING_GROUPS_ALIAS}`
                    && dispatchAction.payload.tableName === TABLE_NAMES.BOOKING
                    && repeatBookingDialog.formData && Object.keys(repeatBookingDialog.formData).length > 0) {
                    dispatch(createRepeatBooking(dispatchAction));
                } else if (dispatchAction.type === `${ENTITY_WINDOW.SUBMIT_UPDATE}_${PLANNER_BOOKING_GROUPS_ALIAS}`
                    && dispatchAction.payload.tableName === TABLE_NAMES.BOOKING
                    && repeatBookingDialog.bookingSeries && Object.keys(repeatBookingDialog.bookingSeries).length > 0
                    && repeatBookingDialog.editType !== EDIT_REPEAT_BOOKING_TYPE.SELECTED_ONLY) {

                    // Change recurrent booking to non-recurrent if recurrent pattern is set to 'does not repeat'
                    if (repeatBookingDialog.formData && repeatBookingDialog.formData.repeatInterval === repeatEveryOptionValue.DOES_NOT_REPEAT) {
                        dispatch(changeToNonRepeatBooking(dispatchAction));

                    } else {
                        if (repeatBookingDialog.editType === EDIT_REPEAT_BOOKING_TYPE.SELECTED_AND_FUTURE) {
                            const payload = {
                                formData: repeatBookingDialog.formData,
                                bookingSeries: repeatBookingDialog.bookingSeries,
                                tableData: dispatchAction.payload.tableData,
                                tableName: dispatchAction.payload.tableName,
                                alias: PLANNER_BOOKING_GROUPS_ALIAS,
                                moduleName,
                                dispatchAction
                            };

                            dispatch(openWarningUpdateWindow(payload));
                        } else {
                            dispatch(updateRepeatBooking(dispatchAction));
                        }
                    }

                } else {
                    if (actionType === `${ENTITY_WINDOW.SUBMIT_UPDATE}_${PLANNER_MASTER_REC_ALIAS}` && dispatchAction.payload.tableName === TABLE_NAMES.RESOURCE) {
                        delete dispatchAction.payload.tableData[RESOURCE_CMEBLUE];
                        delete dispatchAction.payload.tableData[RESOURCE_CMEGREEN];
                        delete dispatchAction.payload.tableData[RESOURCE_CMERED];
                        delete dispatchAction.payload.tableData[RESOURCE_CMEYELLOW];
                    }

                    if (actionType === `${ENTITY_WINDOW.CLOSE}_${moduleName}` && dispatchAction.payload.tableNames.includes(TABLE_NAMES.BOOKING)) {
                        dispatch(clearFieldValueMessages({ tableName: availableData.tableName, fieldName: BOOKING_TIME_ALLOCATION, messageId: boookingJobHoursOverBudgetMessage.id }));
                    }

                    dispatch(promptAction(dispatchAction, pageAlias, context, null, availableData.entity));
                }
            },
            onClose: () => {
                const state = store.getState();
                const propsFromDispatch = stateToProps(state);
                const { operation = ENTITY_WINDOW_OPERATIONS.CREATE, tableName = TABLE_NAMES.BOOKING } = propsFromDispatch;
                const additionalParams = {
                    operation,
                    tableName
                };

                const closeAction = moduleName === ENTITY_WINDOW_MODULES.GLOBAL_CREATE_MODAL
                    ? entityWindowCloseGlobalCreateModal(moduleName)
                    : entityWindowClose(moduleName);

                dispatch(closeAction);
                dispatchAdditionalOnCloseActions(state, dispatch, additionalParams);
                dispatch(clearFieldValueMessages({ tableName, fieldName: BOOKING_TIME_ALLOCATION, messageId: boookingJobHoursOverBudgetMessage.id }));
            },
            onContextualEditStart: (fieldInfo) => {
                dispatch(entityWindowContextualEditStart(moduleName, fieldInfo));
            },
            onContextualEditApply: (fieldInfo) => {
                dispatch(entityWindowContextualEditApply(moduleName, fieldInfo));
            },
            onContextualEditCancel: (fieldInfo) => {
                setTimeout(() => {
                    dispatch(entityWindowContextualEditCancel(moduleName, fieldInfo));
                }, 50);
            },
            onExpandCollapseSection: (tableName, sectionKey, collapsed) => {
                dispatch(entityWindowExpandCollapseSection(moduleName, tableName, sectionKey, collapsed));
            },
            onTabSelected: (selectedTab) => {
                const state = store.getState();
                const propsFromState = stateToProps(state);
                const { activeTab, tableName, entityId } = propsFromState;

                if (activeTab !== selectedTab) {
                    dispatch(entityWindowSetActiveTab(moduleName, selectedTab, entityId));

                    if (selectedTab === ENTITY_WINDOW_TAB_KEYS.HISTORY) {
                        dispatch(loadAudit(tableName, entityId));
                    }
                }
            },
            onBulkEditRemoveItem: (field, batchFields) => {
                const { name: fieldName } = field;
                const state = store.getState();
                const propsFromState = stateToProps(state);
                const { moduleName, sections, tableName, batchIds } = propsFromState;
                let { selectedEditAllFieldsKeys } = propsFromState;
                const elementIndex = selectedEditAllFieldsKeys.indexOf(fieldName);
                const revertEntityAction = getRevertActionToDispatch(field, propsFromState);
                let actions = [];

                if (elementIndex !== -1) {
                    selectedEditAllFieldsKeys.splice(elementIndex, 1);
                }

                selectedEditAllFieldsKeys = handleEditAllSelectedSectionKeys(sections, selectedEditAllFieldsKeys);
                selectedEditAllFieldsKeys = handleEditAllGroupedFieldsSelection(fieldName, selectedEditAllFieldsKeys, tableName);

                actions.push(setBatchFields(moduleName, selectedEditAllFieldsKeys, batchFields));
                actions.push(revertEntityAction);

                batchIds.forEach((entityId) => {
                    actions = actions.concat(getRevertValidationActionsForCustomComponent(moduleName, fieldName, entityId));
                });

                actions = actions.concat(getRevertValidationActionsForCustomComponent(moduleName, fieldName, EDIT_ALL_ENTITY_ID));

                if (isBudgetSectionField(fieldName)) {
                    actions.push(changeReadOnlyFieldsVisibility(moduleName, ENTITY_WINDOW_SECTION_KEYS.BUDGET, tableName, false));
                }

                dispatch(batchActions(actions));
            },
            onAddNewRole: (alias, tableName, collectionAlias, operation, entity, entityId, lazyLoadEntityData = true, actor) => {
                const state = store.getState();
                const draftStatusGuid = getRoleRequestStatusGuidSelector(state)(ROLE_ITEM_STATUS_KEYS.DRAFT);
                const rolerequestGroupGuid = getRoleRequestGroupGuidSelector(state);

                dispatch(
                    addNewEntityAction(
                        alias,
                        tableName,
                        collectionAlias,
                        operation,
                        {
                            [ROLEREQUEST_FIELDS.STATUS_GUID]: draftStatusGuid,
                            [ROLEREQUEST_FIELDS.ROLE_GROUP_GUID]: rolerequestGroupGuid,
                            [ROLEREQUEST_FIELDS.IS_TEMPLATE]: false
                        },
                        entityId,
                        lazyLoadEntityData,
                        actor
                    )
                );
            },
            onCriteriaFieldError: (errors, entityId) => {
                const state = store.getState();
                const propsFromState = stateToProps(state);
                const { entityId: activeEntityId } = propsFromState;
                const roleGuid = entityId || activeEntityId;

                dispatch(setCriteriaFieldsError(errors, roleGuid));
            },
            ...skillsDispatchToProps(dispatch),
            onHeaderButtonClick: (event, actionToDispatch) => {
                dispatch(actionToDispatch());
            },
            onOpenRepeatBookingsModal: () => {
                const state = store.getState();
                const propsFromState = stateToProps(state);
                const { entityId, entity, tableName, collectionAlias } = propsFromState;
                dispatch(repeatBookingOpenDialog({ entityId, entity, tableName, collectionAlias, operation: REPEAT_BOOKING_OPERATION.CREATE }));
            },
            onEditBookingSeries: () => {
                const state = store.getState();
                const propsFromState = stateToProps(state);
                const { tableName, getFieldInfo, moduleName, bookingSeriesInformation, editRepeatBookingType } = propsFromState;


                if (Object.keys(bookingSeriesInformation).length > 0 && editRepeatBookingType !== EDIT_REPEAT_BOOKING_TYPE.ALL) {

                    const booking = JSON.parse(bookingSeriesInformation.bookingseries_booking);

                    if (booking) {
                        const bookingStartDateFieldInfo = getFieldInfo(tableName, BOOKING_START);
                        const bookingEndDateFieldInfo = getFieldInfo(tableName, BOOKING_END);

                        dispatch(entityWindowDrivenFieldChanged(moduleName, bookingStartDateFieldInfo, booking[BOOKING_START]));
                        dispatch(entityWindowDrivenFieldChanged(moduleName, bookingEndDateFieldInfo, booking[BOOKING_END]));
                    }
                }
                dispatch(repeatBookingUpdateEditType(EDIT_REPEAT_BOOKING_TYPE.ALL));
            }
        };
    };

    const mergeProps = (propsFromState, propsFromDispatch, ownProps) => {
        let mergeProps = {
            ...propsFromState,
            ...propsFromDispatch,
            ...ownProps
        };

        mergeProps = {
            ...mergeProps,
            getNormalRenderFieldOptions: getNormalRenderFieldOptionsBuilder(mergeProps)
        };

        mergeProps = {
            ...mergeProps,
            getCriteriaRenderFieldOptions: getCriteriaRenderFieldOptionsBuilder(mergeProps)
        };

        const shouldAddBulkEditTab = isEditAllEntity(mergeProps.entityId);
        let tabs = [];

        if (moduleName === ENTITY_WINDOW_MODULES.ROLE_REQUEST_FORM) {
            tabs.push(getRoleListTab({ ...mergeProps }));
        } else if (moduleName === ENTITY_WINDOW_MODULES.MANAGE_ROLE_TEMPLATES_MODAL) {
            tabs.push(getRoleTemplatesListTab({ ...mergeProps }));
        } else if (shouldAddBulkEditTab) {
            const onBulkFieldAdd = (fieldInfo, fieldValue, shouldValidate = true, shouldApplyValues = true) => {
                const state = store.getState();

                if (shouldApplyValues) {
                    propsFromDispatch.onEntityFieldChange(fieldInfo, fieldValue);
                }

                if (shouldValidate) {
                    const editAllErrors = {
                        [fieldInfo.name]: {
                            errors: getEditAllErrors(state, fieldInfo, fieldValue, state.entityWindow.window[moduleName].windows[EDIT_ALL_ENTITY_ID])
                        }
                    };

                    propsFromDispatch.onFieldError(editAllErrors || {}, EDIT_ALL_ENTITY_ID);
                }
            };

            tabs.push(getBulkEditTab({ ...mergeProps, onBulkFieldAdd }));
        } else {
            tabs.push(getDetailsTab({ ...mergeProps }));
        }

        const { tableName, operation, titleButtonKey } = propsFromState;
        if (shouldShowHistoryTab(tableName, moduleName, operation) && !shouldAddBulkEditTab) {
            tabs.push(
                getHistoryTab({
                    ...mergeProps
                })
            );
        }

        let titleButtonAction = null;

        if (titleButtonKey) {
            titleButtonAction = getTitleButtonActionByKey(titleButtonKey, propsFromState, propsFromDispatch);
        }

        return {
            ...mergeProps,
            tabsConfig: {
                containerProps: {},
                tabs
            },
            titleButtonAction
        };
    };

    let mapStateToProps = stateToProps;
    let mapDispatchToProps = dispatchToProps;
    let mapMergeProps = mergeProps;

    let EntityWindowComponent = getModuleImplementingComponent(moduleName);

    if (EntityWindowComponent === BatchEntityWindow) {
        mapMergeProps = (propsFromState, propsFromDispatch, ownProps) => {
            const previousProps = mergeProps(propsFromState, propsFromDispatch, ownProps);

            return {
                ...previousProps,
                getEntityWindowCarouselComponent
            };
        };
    }

    if (EntityWindowComponent === RoleListEntityWindow) {
        mapMergeProps = (propsFromState, propsFromDispatch, ownProps) => {
            const previousProps = mergeProps(propsFromState, propsFromDispatch, ownProps);

            return {
                ...previousProps,
                getEntityWindowListComponent
            };
        };
    }

    if (EntityWindowComponent === RoleTemplatesListEntityWindow) {
        mapDispatchToProps = (dispatch) => {
            return {
                ...dispatchToProps(dispatch),
                createNewTemplate: (event) => {
                    const state = store.getState();
                    const getCollectionAliasSelector = getCollectionAliasSelectorCreator();
                    const collectionAlias = getCollectionAliasSelector(state)(TABLE_NAMES.ROLEREQUEST);
                    event.stopPropagation();

                    dispatch(
                        entityWindowOpenRequest(
                            ENTITY_WINDOW_MODULES.CREATE_ROLE_TEMPLATE_MODAL,
                            TABLE_NAMES.ROLEREQUEST,
                            collectionAlias,
                            ENTITY_WINDOW_OPERATIONS.CREATE,
                            {
                                [ROLEREQUEST_FIELDS.HASCRITERIA]: true,
                                [ROLEREQUEST_FIELDS.IS_TEMPLATE]: true,
                                [ROLEREQUEST_FIELDS.STATUS_GUID]: getTableDataRoleRequestStatusGuidSelector(state)(ROLE_ITEM_STATUS_KEYS.DRAFT)
                            }
                        )
                    );
                },
                onClose: () => {
                    const state = store.getState();
                    const isTemplateEdited = getHasWindowInContextualEditSelector(state)(ENTITY_WINDOW_MODULES.MANAGE_ROLE_TEMPLATES_MODAL, ROLEREQUEST_FIELDS.DESCRIPTION);

                    if (!isTemplateEdited) {
                        dispatch(entityWindowClose(ENTITY_WINDOW_MODULES.MANAGE_ROLE_TEMPLATES_MODAL));
                    }
                }
            };
        };

        mapMergeProps = (propsFromState, propsFromDispatch, ownProps) => {
            const previousProps = mergeProps(propsFromState, propsFromDispatch, ownProps);

            return {
                ...previousProps,
                getEntityWindowRoleTemplatesListComponent
            };
        };
    }

    if (hotKeysProps) {
        const { hotKeysConfig, triggerHotKeyAction, disableHotKeyBubbling } = hotKeysProps;

        mapStateToProps = (state) => {
            return {
                ...stateToProps(state),
                hotKeysConfig,
                disableHotKeyBubbling
            };
        };

        mapDispatchToProps = (dispatch) => {
            return {
                ...dispatchToProps(dispatch),
                triggerHotKey: (hotKey) => {
                    const state = store.getState();
                    triggerHotKeyAction(state, dispatch, hotKey, hotKeysConfig);
                }
            };
        };

        EntityWindowComponent = withHotKeys(getModuleImplementingComponent(moduleName));
    }

    const ConnectedEntityWindow = connect(
        mapStateToProps,
        mapDispatchToProps,
        mapMergeProps
    )(EntityWindowComponent);

    return ConnectedEntityWindow;
};

export {
    createEntityWindow
};