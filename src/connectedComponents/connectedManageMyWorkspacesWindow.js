import { connect } from 'react-redux';
import { omit } from '../utils/commonUtils';
import { shouldSaveIfAnyChanges } from '../utils/workspaceUtils';
import ManagePlansWindow from '../lib/managePlansWindow';
import { getOrderedPrivateWorkspaces, getOrderedPublicWorkspaces, getWorkspacesForInsert,
    getDefaultWorkspaceStructure, getWorkspaceSettings, getSelectedWorkspaceGuid,
    workspaceBelongsToResource, getSaveAsNewPlanWorkspaceSelector } from '../selectors/workspaceSelectors';
import { hasFunctionalAccessGlobal } from '../selectors/applicationFnasSelectors';
import { getApplicationUserId } from '../selectors/applicationUserSelectors';
import { setManageMyPlansWindowVisibility } from '../actions/managePlansSectionActions';
import { getManageMyPlansWindowStaticMessagesSelector } from '../selectors/workspaceSelectors';
import { WORKSPACE_ACCESS_TYPES, WORKSPACE_EDIT_RIGHTS } from '../constants/workspaceConsts';
import { JOBS_PAGE_ALIAS } from '../constants/jobsPageConsts';
import { promptAction } from '../actions/promptActions';
import * as workspaceActions from '../actions/workspaceActions';
import * as managePlansSectionActions from '../actions/managePlansSectionActions';
import { getFeatureFlagSelector } from '../selectors/featureManagementSelectors';
import { FEATURE_FLAGS } from '../constants/globalConsts';

const applyManageRightsToPlans = (plans, userId, hasManagePublicPlansAccess) => {
    return plans.map(plan => {
        const canManage = workspaceBelongsToResource(plan, userId) || hasManagePublicPlansAccess;
        return {
            ...plan,
            canManage
        };
    });
};

// Jobs page specific mapStateToProps that uses listPage.workspaces instead of plannerPage.workspaces
const mapStateToProps = state => {
    // Safely access listPage.workspaces with fallback
    const listPageWorkspaces = state.listPage.workspaces || {};
    let { selected } = listPageWorkspaces;
    const { workspacesInEditMode, managePublicPlansFNAName } = state.plannerPage.manageMyPlans;
    const workspacesInEditModeGuids = Object.keys(workspacesInEditMode);

    // Safely get workspaces for insert with fallback to empty array
    const plansForInsertInEditMode = listPageWorkspaces.workspacesStructureChanges ?
        getWorkspacesForInsert(listPageWorkspaces).filter(workspace => workspacesInEditModeGuids.includes(workspace.workspace_guid)) :
        [];

    // Safely access workspacesStructure.map with fallback to empty object
    const workspacesStructureMap = (listPageWorkspaces.workspacesStructure && listPageWorkspaces.workspacesStructure.map) || {};

    const privatePlans = [...getOrderedPrivateWorkspaces(plansForInsertInEditMode), ...getOrderedPrivateWorkspaces(workspacesStructureMap)];
    const defaultWorkspaceStructure = getDefaultWorkspaceStructure(listPageWorkspaces);
    const publicPlans = applyManageRightsToPlans(
        [...getOrderedPublicWorkspaces(plansForInsertInEditMode), ...getOrderedPublicWorkspaces(workspacesStructureMap)],
        getApplicationUserId(state),
        hasFunctionalAccessGlobal(state, managePublicPlansFNAName)
    );
    const visible = state.plannerPage.manageMyPlans.visible;
    const getState = () => state;
    const staticMessages = getManageMyPlansWindowStaticMessagesSelector(state);
    const listPageAndBulkUpdateFeatureFlag = getFeatureFlagSelector(FEATURE_FLAGS.LIST_PAGE_AND_BULK_UPDATE)(state);

    return {
        visible,
        staticMessages,
        privatePlansColumn: {
            type: WORKSPACE_ACCESS_TYPES.PRIVATE,
            title: listPageAndBulkUpdateFeatureFlag ? staticMessages.privateWorkspacesColumnTitle : staticMessages.privatePlansColumnTitle,
            activePlanGuid: selected,
            plans: privatePlans
        },
        publicPlansColumn: {
            type: WORKSPACE_ACCESS_TYPES.PUBLIC,
            title: listPageAndBulkUpdateFeatureFlag ? staticMessages.publicWorkspacesLabel : staticMessages.publicPlansLabel,
            plans: publicPlans,
            activePlanGuid: selected
        },
        workspacesInEditMode,
        defaultWorkspace: defaultWorkspaceStructure,
        getState,
        listPageAndBulkUpdateFeatureFlag
    };
};

const mapDispatchToProps = dispatch => {
    return {
        onClose: () => {
            dispatch(setManageMyPlansWindowVisibility(false));
        },
        promptActions: {
            promptDeletePlan: (workspaceGuid) => {
                const dispatchAction = workspaceActions.deleteWorkspace(workspaceGuid);
                dispatch(promptAction(dispatchAction, JOBS_PAGE_ALIAS));
            },
            promptRenamePlan: (workspaceGuid, newDescription) => {
                const dispatchAction = workspaceActions.renameWorkspace(workspaceGuid, { 'workspace_description': newDescription });
                dispatch(promptAction(dispatchAction, JOBS_PAGE_ALIAS));
            },
            promptSelectPlan: (workspaceGuid, getState) => {
                const state = getState();
                const workspaces = state.listPage.workspaces;
                const currentWorkspaceGuid = getSelectedWorkspaceGuid(workspaces);

                dispatch(setManageMyPlansWindowVisibility(false));

                if (shouldSaveIfAnyChanges(workspaces, currentWorkspaceGuid)) {
                    const dispatchAction = workspaceActions.saveWorkspaceIfAnyChanges(currentWorkspaceGuid, workspaceGuid);
                    dispatch(promptAction(dispatchAction, JOBS_PAGE_ALIAS));
                } else {
                    dispatch(workspaceActions.digestSelectWorkspace(workspaceGuid));
                }
            },
            promptMovePlan: (uuid, accessType, editRights) => {
                const workspaceData = {
                    'workspace_accesstype': accessType,
                    'workspace_editrights': editRights
                };
                const dispatchAction = workspaceActions.moveWorkspace(uuid, workspaceData);
                dispatch(promptAction(dispatchAction, JOBS_PAGE_ALIAS));
            }
        },
        sectionActions :{
            WsAddEditMode :(guid, type) => {
                dispatch(managePlansSectionActions.manageWorkspacesAddEditMode(guid, type));
            },
            WsRemoveEditMode:(guid) => {
                dispatch(managePlansSectionActions.manageWorkspacesRemoveEditMode(guid));
            }
        },
        actions :{
            renamePlan: (workspaceGuid,newDescription) => {
                dispatch(workspaceActions.renameWorkspace(workspaceGuid, { 'workspace_description' : newDescription }));
            },
            digestCreatePlan: (defaultWorkspaceGuid, privatePlans, newPlanLabel) => {
                const doCreate = false;
                dispatch(workspaceActions.digestCreateWorkspace(WORKSPACE_ACCESS_TYPES.PRIVATE, WORKSPACE_EDIT_RIGHTS.EDIT, defaultWorkspaceGuid, privatePlans, newPlanLabel, doCreate));
            },
            createPlan: (uuid, newDescription) => {
                const selectCreatedWorkspace = false;
                dispatch(workspaceActions.digestWorkspaceStructureChange(uuid, { 'workspace_description' : newDescription }));
                dispatch(workspaceActions.createWorkspace(uuid, selectCreatedWorkspace));
            },
            removeCreatePlanChange: (uuid) => {
                dispatch(workspaceActions.removeCreateWorkspaceChange(uuid));
            },
            copyPlan: (newWorkspaceTemplateGuid, newWorkspaceAccessType, newWorkspaceEditRights, originalWorkspaceName, getState) => {
                const doCreate = true;
                const templateWorkspaceSettings = getWorkspaceSettings(getState().listPage.workspaces, newWorkspaceTemplateGuid);
                const newWorkspaceSettings = getSaveAsNewPlanWorkspaceSelector(getState().listPage.workspaces);
                const { workspaceColourthemeGuid = null, workspaceCustomColourTheme = [] } = newWorkspaceSettings;

                if (!templateWorkspaceSettings) {
                    const selectWorkspace = false;
                    dispatch(workspaceActions.loadCopyWorkspaceTemplate(newWorkspaceAccessType, newWorkspaceEditRights, newWorkspaceTemplateGuid, doCreate, originalWorkspaceName, selectWorkspace));
                } else {
                    dispatch(workspaceActions.digestCopyWorkspace(newWorkspaceAccessType, newWorkspaceEditRights, newWorkspaceTemplateGuid, doCreate, originalWorkspaceName, workspaceColourthemeGuid, workspaceCustomColourTheme));
                }
            },
            movePlan: (uuid, accessType, editRights) => {
                const workspaceData = {
                    'workspace_accesstype': accessType,
                    'workspace_editrights': editRights
                };
                dispatch(workspaceActions.moveWorkspace(uuid, workspaceData));
            }
        }
    };
};

const mergeProps = (propsFromState, propsFromDispatch, ownProps) => {
    return {
        ...propsFromDispatch,
        ...ownProps,
        ...omit(propsFromState, ['getState']),
        promptActions: {
            promptDeletePlan: (workspaceGuid) =>
                propsFromDispatch.promptActions.promptDeletePlan(workspaceGuid),
            promptRenamePlan: (workspaceGuid, newDescription) =>
                propsFromDispatch.promptActions.promptRenamePlan(workspaceGuid, newDescription),
            promptSelectPlan: (workspaceGuid) => {
                propsFromDispatch.promptActions.promptSelectPlan(workspaceGuid, propsFromState.getState);
            },
            promptMovePlan: (workspaceGuid, accessType, editRights) => {
                propsFromDispatch.promptActions.promptMovePlan(workspaceGuid, accessType, editRights);
            }
        },
        actions: {
            ...propsFromDispatch.actions,
            copyPlan: (newWorkspaceTemplateGuid, newWorkspaceAccessType, newWorkspaceEditRights, originalWorkspaceName) => {
                propsFromDispatch.actions.copyPlan(newWorkspaceTemplateGuid, newWorkspaceAccessType, newWorkspaceEditRights, originalWorkspaceName, propsFromState.getState);
            },
            digestCreatePlan: (defaultWorkspaceGuid) => {
                propsFromDispatch.actions.digestCreatePlan(defaultWorkspaceGuid, propsFromState.privatePlansColumn.plans, propsFromState.staticMessages.newPlanLabel);
            }
        }
    };
};

const ConnectedManageMyWorkspacesWindow = connect(
    mapStateToProps,
    mapDispatchToProps,
    mergeProps
)(ManagePlansWindow);

export { ConnectedManageMyWorkspacesWindow };
