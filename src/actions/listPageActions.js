import { COMMAND_BAR_WORKSPACES_SECTION, JOBS_COMMAND_BAR, LIST_PAGE_ACTIONS } from "./actionTypes";

export function updateListView(payload) {
    return {
        type: LIST_PAGE_ACTIONS.UPDATE_LIST_VIEW,
        payload
    };
}

// List page command bar actions
export function commandBarSetSectionVisibility(sectionKey, visible, pageAlias) {
    return {
        type: JOBS_COMMAND_BAR.SET_SECTION_VISIBILITY,
        payload: {
            sectionKey,
            visible,
            pageAlias
        }
    };
}

export function commandBarPopulateWorkspacesSection(workspaces) {
    return {
        type: COMMAND_BAR_WORKSPACES_SECTION.POPULATE,
        payload: {
            workspaces,
            selectedWorkspaceGuid: workspaces?.selected
        }
    }
}

export function updateWorkspaceViewType(payload) {
    return {
        type: LIST_PAGE_ACTIONS.UPDATE_WORKSPACE_VIEW_TYPE,
        payload
    }
}

export function loadListPageWorkspaces(groupByType) {
    return {
        type: LIST_PAGE_ACTIONS.LOAD_WORKSPACES,
        payload: {
            groupByType,
        }
    }
}

export function digestCreateListPageWorkspace(wsAccessType, wsEditRights, newWorkspaceTemplateGuid, privatePlans, newPlanLabel, doCreate, selectCreatedWorkspace) {
    return {
        type: LIST_PAGE_ACTIONS.CREATE_WORKSPACE,
        payload: {
            wsAccessType,
            wsEditRights,
            newWorkspaceTemplateGuid,
            privatePlans,
            newPlanLabel,
            doCreate,
            selectCreatedWorkspace
        }
    }
}

export function digestCreateListPageWorkspaceSuccess(alias, payload, data) {
    return {
        type: LIST_PAGE_ACTIONS.CREATE_WORKSPACE_SUCCESSFUL,
        payload: {
            ...payload,
            data
        }
    }
}
