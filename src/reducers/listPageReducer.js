import initialState from '../state/initialState';
import { LIST_PAGE_ACTIONS } from '../actions/actionTypes';
import { buildStructure } from '../state/skillStructure/mapCollection';
import { configureWorkspaceStructure } from '../reducers/plannerPageReducer/workspacesReducer';

export default (state = initialState.listPage, action) => {
    switch (action.type) {
        case LIST_PAGE_ACTIONS.UPDATE_LIST_VIEW: {
            return {
                ...state,
                activeListView: action.payload
            }
        }
        case LIST_PAGE_ACTIONS.UPDATE_WORKSPACE_VIEW_TYPE: {
            return {
                ...state,
                wsViewType: action.payload
            }
        }
        case LIST_PAGE_ACTIONS.LOAD_WORKSPACES_SUCCESSFUL: {
            const { payload } = action;
            const { data } = payload;

            // The data comes as [workspaceStructures, mostRecentlyUsedWorkspaces] from the API
            const workspaceStructures = data[0] || [];
            const mostRecentlyUsedWorkspaces = data[1] || [];



            // Configure workspace structures like planner page does
            const configuredWorkspaces = workspaceStructures.map(workspace => configureWorkspaceStructure(workspace));

            // Transform the workspace data into the expected structure
            const workspacesStructure = buildStructure("workspace_guid", configuredWorkspaces);

            // Get the first most recently used workspace or first workspace as selected
            const selectedWorkspace = mostRecentlyUsedWorkspaces.length > 0
                ? mostRecentlyUsedWorkspaces[0].workspace_guid
                : (workspaceStructures.length > 0 ? workspaceStructures[0].workspace_guid : null);

            return {
                ...state,
                workspaces: {
                    ...state.workspaces,
                    workspacesStructure,
                    selected: state.workspaces?.selected || selectedWorkspace,
                    mostRecentlyUsed: mostRecentlyUsedWorkspaces.map(ws => ws.workspace_guid),
                    // Add empty workspacesSettings structure that selectors expect
                    workspacesSettings: {
                        mapField: 'workspace_guid',
                        orderedKeys: configuredWorkspaces.map(ws => ws.workspace_guid),
                        map: configuredWorkspaces.reduce((acc, ws) => {
                            acc[ws.workspace_guid] = { workspace_guid: ws.workspace_guid };
                            return acc;
                        }, {})
                    },
                    // Add empty workspacesStructureChanges that selectors might expect
                    workspacesStructureChanges: state.workspaces?.workspacesStructureChanges || {
                        mapField: "id",
                        inserts: { orderedKeys: [], map: {} },
                        updates: { orderedKeys: [], map: {} },
                        deletes: { orderedKeys: [], map: {} }
                    }
                }
            }
        }
        default: {
            return state;
        }

    }
};