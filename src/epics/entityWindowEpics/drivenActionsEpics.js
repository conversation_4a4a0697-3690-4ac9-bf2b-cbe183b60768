import { of, from, merge, empty, concat, EMPTY } from 'rxjs';
import { map, filter, switchMap, mergeMap, takeUntil } from 'rxjs/operators';
import { isEmpty, unionBy } from 'lodash';
import { combineEpics } from 'redux-observable';
import * as actionTypes from '../../actions/actionTypes';
import { setFieldValueExplanations, clearFieldValueExplanations } from '../../actions/fieldValueExplanationsActions';
import { entityWindowHighlightField, entityWindowHideSectionContent, entityWindowSetMessages, changeReadOnlyFieldsVisibility, loadFieldValues, entityWindowFieldChanged, entityWindowFieldChangedForAllEntities, entityWindowFieldChangedContextually, entityWindowUpdateMessages } from '../../actions/entityWindowActions';
import { getEntityWindowTableName, getEntityWindowSectionByKey, isEntityWindowSectionContentHidden, getEntityWindowOperation, getActiveEntityID, createGetLinkedDataWrappedSelector, createGetActiveWindowSelector, getEntityWindowEntity, getEntityWindow } from '../../selectors/entityWindowSelectors';
import { getEntityWindowUIEntity } from '../../selectors/entityWindowUiEntitySelectors';
import {
    chargeModeSectionFieldByTableName,
    dateRangeSectionFieldByTableName,
    timeAllocationNonWorkSectionFieldByTableName,
    resourceSectionFieldByTableName,
    rejectReasonSectionFieldByTableName,
    costPerHourFieldByTableName,
    revenuePerHourFieldByTableName,
    roleRequestGroupGuidField,
    endDateSectionFieldByTableName,
    startDateSectionFieldByTableName,
    roleRequestFullTimeEquivalentField,
    bookingStatusSectionField
} from '../../state/entityWindow/fieldsConfig';
import { hasFieldValueExplanation } from '../../selectors/fieldValueExplanationsSelectors';
import { isSubmittableField, getAppFieldValue, getTimeAllocationField, calculateBudgetConsumed, calculateProfitMargin, calculateTotalProfit, getFieldByTableName } from '../../utils/fieldUtils';
import { UNSPECIFIED_FIELD_VALUE_EXPLANATION_KEY, REJECT_REASON_FIELD_VALUE_EXPLANATION_KEY, ROLEREQUEST_FTE_FIELD_EXPLANATION_KEY, ACTIONABLE_FIELD_VALUE_EXPLANATION_KEY } from '../../constants/fieldValueExplanationsConsts';
import { JOB_BUDGET_CONSUMED, JOB_PROFITMARGIN, JOB_TOTALPROFIT, BOOKING_GUID, CHARGERATE_CURRENT_VALUE_FIELDNAME, HOURS_PER_DAY_FALLBACK_VALUE, resourceGuidsFieldByTableName, RESOURCE_DESCRIPTION, RESOURCE_GUID, ROLEREQUEST_DATE_RANGE, ROLEREQUEST_FIELDS, ROLEREQUEST_TIME_ALLOCATION, VALIDATION_REQUIRED_FIELDS, BOOKING_START, RESOURCE_USERSTATUS, RESOURCE_GUIDS, JOB_BOOKINGS_CALC_FIELDS, JOB_GUID, BOOKING_JOB_GUID, BOOKING_STATUS, BOOKING_TIME_ALLOCATION } from '../../constants/fieldConsts';
import { getTableDatasLoadedActions } from '../../utils/linkedDataUtils';
import { JOBS_PAGE_ALIAS, PAGE_NAMES, PLANNER_PAGE_ALIAS, ROLE_GROUP_LIST_PAGE } from '../../constants';
import { BOOKING_TYPES, PLANNER_TABLE_DATAS_SUFFIX, UNASSIGNED_BOOKINGS_RESOURCE } from '../../constants/plannerConsts';
import { getEntityOptionFields } from '../../connectedComponents/connectedEntityLookupWindow';
import { getFieldInfoSelector } from '../../selectors/tableStructureSelectors';
import { getFieldTableName, getGridDatasAlias, getTableDatasAlias } from '../../utils/tableStructureUtils';
import { avatarImageLoaded, resourceAvatarLoaded } from '../../selectors/avatarSelectors';
import { loadAvatars, loadAvatarSuccess } from '../../actions/avatarActions';
import { AVATAR_SIZES } from '../../constants/avatarConsts';
import { entityWindowDrivenFieldChanged, entityWindowDrivenFieldChangedContextually } from '../../actions/entityWindowActions';
import {
    setBookingWorkNonWorkDaysExplanation,
    loadResourceChargeRateCurrentValue,
    updateBookingChargeRateValues,
    updateBookingTimeAllocationValues,
    clearBookingWorkNonWorkDaysExplanation,
    updateOverlappingBookings,
    validateMultipleEntities,
    updateRolerequestEntityWindowSectionMessages,
    setFieldValueMessages,
    clearFieldValueMessages
} from '../../actions/entityWindowDrivenActions';
import { getFieldInfo, getTableStructure } from '../../selectors/tableStructureSelectors';
import { ENTITY_WINDOW_CLEAR_HIGHLIGHT_DELAY, ENTITY_WINDOW_SECTION_KEYS, ENTITY_WINDOW_TAB_KEYS, ENTITY_WINDOW_OPERATIONS, EDIT_ALL_ENTITY_ID } from '../../constants/entityWindowConsts';
import { getTimeAllocationReadOnlyExplanation, getDateRangeReadOnlyExplanation, getBookingResourceDescriptionReadOnlyExplanation, getRejectReasonExplanation, getRepeatBookingExplanation } from '../../utils/fieldControlUtils';
import { injectDynamicConfiguration } from '../../actions/entityWindowDrivenActions';
import { ENTITY_WINDOW_MODULES } from '../../constants';
import { getEntityDynamicConfig } from '../../utils/entityDynamicConfigUtils';
import { DIARY_GROUP } from '../../constants/fieldConsts';
import { FEATURE_FLAGS, FILTER_FIELD_NAMES, OPERATORS, TABLE_NAMES } from '../../constants/globalConsts';
import { API_KEYS } from '../../constants/apiConsts';
import { combineAPIEpics } from '../middlewareExtensions';
import { getTranslationsSelector } from '../../selectors/internationalizationSelectors';
import { loadAudit } from '../../actions/auditActions';
import { formValidationMapAction } from './index';
import { getIsRoleEditable } from '../../utils/rolegroupDetailsPageUtils';
import { loadSingleAvatar$ } from '../avatarEpics';
import { loadMoreTableDataSuccess, patchTableDataSuccess } from '../../actions/tableDataActions';
import { CRITERIA_SECTIONS, ROLE_ITEM_STATUS_KEYS } from '../../constants/rolesConsts';
import { roleResourcesHasUnassignedResource, roleResourcesReadyForBookingRequest, getEntityHasMultipleResources, buildValidateRichFieldsPayload } from '../../utils/entityWindowUtils';
import { patchPagedDataSuccess } from '../../actions/pagedDataActions';
import { getCurrentPageAliasSelector, getNavPageTitleSelector } from '../../selectors/navigationSelectors';
import { updatePageParams } from '../../actions/pageStateActions';
import { getIsCriteriaRole } from '../../utils/roleRequestsUtils';
import { ROLE_INBOX_PAGE_ALIAS, ROLE_INBOX_PAGE_TABLE_DATA_ALIAS } from '../../constants/roleInboxPageConsts';
import { isBatchWindowModal, isEmptyObject, isWindowModal } from '../../utils/commonUtils';
import { getHasHiddenRequirementsSelector } from '../../selectors/requirementsSelectors';
import { isRoleInboxPageDp } from '../../utils/roleInboxActionsUtils';
import { roleRequestBookingFunctionalAccessExcludeCondition } from '../../utils/entityWindowConditionsUtils';
import { populateStringTemplates, buildCommonEntityAliasesPlaceholdersSelector } from '../../utils/translationUtils';
import { getPageTableDatasSelector } from '../../selectors/tableDataSelectors';
import { getIsMultipleAssigneesEnabled } from '../../selectors/functionalityConfigurationSelectors';
import { getAccessibleEntitiesIdsSelector } from '../../selectors/userEntityAccessSelectors';
import { getPageRoleRequestStatusDescriptionSelector } from '../../selectors/roleRequestStatusSelectors';
import { updateBudgetSectionMessages } from '../roleGroupDetailsPageEpics';
import { TABLE_VIEW_PAGE_ALIAS } from '../../constants/tableViewPageConsts';
import { getEntityWindowMessagesSelector, getBookingBudgetMessageOptionsSelector } from '../../selectors/entityWindowMessagesSelectors';
import { updateFieldValue } from '../../actions/roleGroupDetailsActions';
import { TableSelectionBuilder } from '../../utils/plannerDataQueryBuilder';
import { getBudgetSectionKey } from '../../utils/entityWindowSectionsUtils';
import jobDrivenActionEpics from './jobDrivenActionEpics';
import { roleGroupDrivenActionsEpics$ } from './roleGroupDrivenActionEpics';
import { getCriteriaRoleAssigneesIdsSelector } from '../../selectors/roleRequestsSelector';
import { boookingJobHoursOverBudgetMessage } from '../../utils/messages';
import { getFeatureFlagSelector } from '../../selectors/featureManagementSelectors';

const { REJECTED } = ROLE_ITEM_STATUS_KEYS;

const { ENTITY_VALIDATION_API_KEY, TABLE_DATA } = API_KEYS;

const isRoleStatusRejected = (roleStatus) => {
    return roleStatus === REJECTED;
};

const isBookingIdNeeded = (operation, tableName) => {
    const operationOfInterest = [ENTITY_WINDOW_OPERATIONS.EDIT, ENTITY_WINDOW_OPERATIONS.CONTEXTUAL_EDIT];

    return operationOfInterest.includes(operation) && tableName === TABLE_NAMES.BOOKING;
};

const getEntityWindowLoadMoreTableDataActions = (payload, state) => {
    const { fieldInfo, fieldValue, moduleName, tableDatasAlias = `${PLANNER_PAGE_ALIAS}_${PLANNER_TABLE_DATAS_SUFFIX}` } = payload;
    const tableName = getEntityWindowTableName(state.entityWindow, moduleName);
    const primaryFieldTableName = getFieldTableName(fieldInfo, tableName);

    const tableDatas = getEntityOptionFields(fieldValue, primaryFieldTableName);

    return !isEmpty(fieldValue) && fieldValue.id
        ? [
            loadMoreTableDataSuccess(
                tableDatasAlias,
                { tableDataGuid: primaryFieldTableName, tableNames: [primaryFieldTableName] },
                [tableDatas]
            )
        ]
        : [];
};

const getSubmitEntityField = (field, entity, uiEntity, tableName) => {
    const timeAllocationField = getTimeAllocationField(tableName, entity);
    const { fields: timeAllocationFields } = timeAllocationField;

    const isTimeAllocationField = timeAllocationFields.some(taField => taField.name === field);
    const selectedField = timeAllocationFields[uiEntity[timeAllocationField.valueKey.name].value] || {};
    const isSelectedTimeAllocationField = selectedField.name === field;

    let fieldValue;

    if (isTimeAllocationField) {
        //ommit not selected time allocation fields
        const fieldIsPartOfSelectedOption = selectedField.fields != null && selectedField.fields.some(subField => {
            return subField.name === field;
        });

        if (isSelectedTimeAllocationField || fieldIsPartOfSelectedOption) {
            fieldValue = uiEntity[field].value;
        }
    }
    else if (field === timeAllocationNonWorkSectionFieldByTableName[tableName].name) {
        fieldValue = uiEntity[field].value === true || uiEntity[field].value === 1 ? 1 : 0;
    }
    else {
        fieldValue = uiEntity[field].value;
    }

    return fieldValue;
};

export const getValidateSubmitEntity = (uiEntity, state$, moduleName) => {
    const state = state$.value;
    const tableName = getEntityWindowTableName(state.entityWindow, moduleName);
    const entityWindow = getEntityWindow(state, moduleName);
    const { operation: entityWindowOperation, entityId, batchIds, activeEntity } = entityWindow;

    const isFieldPopulated = (fieldValue) => {
        return fieldValue !== undefined && fieldValue !== null;
    };

    let validateRichFieldsPayload = Object.keys(uiEntity).reduce((accumulator, field) => {
        const currFieldInfo = getFieldInfo(getTableStructure(state), tableName, field);
        let submitEntityField;
        if (
            currFieldInfo
            && (isSubmittableField(field, tableName, currFieldInfo) || VALIDATION_REQUIRED_FIELDS[tableName].indexOf(field) !== -1)
            && uiEntity[field].errors
            && uiEntity[field].errors.length === 0
        ) {
            switch (tableName) {
                case TABLE_NAMES.BOOKING:
                case TABLE_NAMES.ROLEREQUEST: {
                    const entity = getEntityWindowEntity(state.entityWindow, moduleName);
                    submitEntityField = getSubmitEntityField(field, entity, uiEntity, tableName);
                    if (isFieldPopulated(submitEntityField)) {
                        accumulator[field] = submitEntityField;
                    }
                    break;
                }
                default:
                    if (isFieldPopulated(uiEntity[field].value)) {
                        accumulator[field] = uiEntity[field].value;
                    }
            }
        }

        return accumulator;
    }, {});

    return isBookingIdNeeded(entityWindowOperation, tableName)
        ? {
            ...validateRichFieldsPayload,
            [BOOKING_GUID]: batchIds && activeEntity !== EDIT_ALL_ENTITY_ID ? activeEntity : entityId }
        : validateRichFieldsPayload;
};

export const validateEntityFields$ = (uiEntity, tableName, apis) => {
    return apis[ENTITY_VALIDATION_API_KEY].validateTableAccessData$(tableName, uiEntity);
};

const validateEntityRichFields$ = (payload, tableName, apis) => {
    return apis[ENTITY_VALIDATION_API_KEY].validateTableAccessRichData$(tableName, payload);
};

const updateTimeAllocationReadOnlyExplanation = (moduleName, data, state$) => {
    const state = state$.value;
    const translations = getTranslationsSelector(state$.value, { sectionName: 'entityWindow' });
    const tableName = getEntityWindowTableName(state$.value.entityWindow, moduleName);
    const commonEntityAliases = buildCommonEntityAliasesPlaceholdersSelector(state);
    const explanation = getTimeAllocationReadOnlyExplanation(data, translations, tableName, commonEntityAliases);
    const { name } = getTimeAllocationField(tableName, data);

    const explanations = {
        [UNSPECIFIED_FIELD_VALUE_EXPLANATION_KEY]: explanation
    };

    return setFieldValueExplanations(moduleName, tableName, name, explanations);
};

const updateRoleRejectReasonExplanation = (moduleName, data, state$) => {
    const state = state$.value;
    const tableName = getEntityWindowTableName(state.entityWindow, moduleName);
    const explanation = getRejectReasonExplanation(data);
    const { name } = rejectReasonSectionFieldByTableName[tableName];

    const explanations = {
        [REJECT_REASON_FIELD_VALUE_EXPLANATION_KEY]: explanation
    };

    return setFieldValueExplanations(moduleName, tableName, name, explanations);
};

const getDataGridPageRowUpdateAction = (state, moduleName, tableName, data, dependantFields = []) => {
    const currentPage = getCurrentPageAliasSelector(state);
    let updateActions = [];

    if (dependantFields.length > 0) {
        const { entityId } = createGetActiveWindowSelector()({ entityWindow: state.entityWindow.window[moduleName] });
        const patchPayload = { tableName, tableDataEntryGuid: entityId, moduleName };

        let tableData = { [ROLEREQUEST_FIELDS.GUID]: entityId };
        dependantFields.forEach(field => tableData[field] = data[field]);

        updateActions.push(createPatchDataSuccessAction(patchPayload, tableData, currentPage));
    }

    return updateActions;
};

export const handleBarTimeAllocationValues$ = (action$, state$, { apis }) => {
    return action$
        .ofType(actionTypes.ENTITY_WINDOW.DRIVEN_FIELD_ACTIONS.UPDATE_BAR_TIME_ALLOCATION_VALUES)
        .pipe(
            switchMap((action) => {
                const { moduleName } = action.payload;
                const uiEntity = getEntityWindowUIEntity(state$.value.entityWindow, moduleName);
                const tableName = getEntityWindowTableName(state$.value.entityWindow, moduleName);

                return validateEntityFields$(getValidateSubmitEntity(uiEntity, state$, moduleName), tableName, apis);
            },
            (action, data) => { return { action, data }; }),
            switchMap(result => {
                const { action, data } = result;
                const state = state$.value;
                const { entityWindow } = state;
                const { moduleName, isContextuallyEditing, editedFieldName } = action.payload;
                const uiEntity = getEntityWindowUIEntity(entityWindow, moduleName) || {};
                const entity = getEntityWindowEntity(entityWindow, moduleName) || {};
                const tableName = getEntityWindowTableName(entityWindow, moduleName);

                const validationDataOnlyEntity = Object.keys(data)
                    .filter(key => data[key])
                    .reduce((acc, curKey) => {
                        acc[curKey] = data[curKey];

                        return acc;
                    }, {});

                const mergedEntity = { ...entity, ...validationDataOnlyEntity };
                const timeAllocationField = getTimeAllocationField(tableName, mergedEntity);

                const { fields: timeAllocationFields, valueKey: { name: leadingFieldName } } = timeAllocationField;
                const isTimeAllocationField = timeAllocationFields.some(field => field.name === editedFieldName);
                const currentTimeAllocationIndex = data[leadingFieldName];
                const activeTimeAllocationFieldName = timeAllocationFields[currentTimeAllocationIndex].name;

                if (!data) {
                    return [];
                }

                const fieldChangeAction = isContextuallyEditing ? entityWindowDrivenFieldChangedContextually : entityWindowDrivenFieldChanged;

                let ewFieldChangeActions = [];

                Object.keys(data)
                    .filter((fieldName) =>
                        fieldName !== editedFieldName
                        && fieldName !== activeTimeAllocationFieldName
                        && timeAllocationFields.some(field => field.name === fieldName))
                    .forEach((fieldName) => {
                        let value;

                        switch (fieldName) {
                            case ROLEREQUEST_FIELDS.FTE:
                            case ROLEREQUEST_FIELDS.HOURS_PER_DAY:
                            case ROLEREQUEST_FIELDS.RESOURCE_DEMAND:
                                value = data[`${tableName}_${TABLE_NAMES.RESOURCE}_guid`] ? data[fieldName] : uiEntity[fieldName].value;
                                break;
                            default:
                                value = data[`${tableName}_${TABLE_NAMES.RESOURCE}_guid`] ? data[fieldName] : null;
                                break;
                        }

                        const currFieldInfo = getFieldInfo(getTableStructure(state$.value), tableName, fieldName);
                        ewFieldChangeActions.push(fieldChangeAction(moduleName, currFieldInfo, value));
                    });

                ewFieldChangeActions.push(updateTimeAllocationReadOnlyExplanation(moduleName, mergedEntity, state$));

                if (!isTimeAllocationField && editedFieldName) {
                    ewFieldChangeActions.push(
                        entityWindowHighlightField(moduleName, timeAllocationField.name, ENTITY_WINDOW_CLEAR_HIGHLIGHT_DELAY)
                    );
                }

                ewFieldChangeActions.push(updateOverlappingBookings(moduleName));

                if (isRoleInboxPageDp(moduleName) && editedFieldName === ROLEREQUEST_DATE_RANGE) {
                    const dependantFields = [ROLEREQUEST_FIELDS.LOADING, ROLEREQUEST_FIELDS.TIME, ROLEREQUEST_FIELDS.HOURS_PER_DAY];
                    ewFieldChangeActions.push(...getDataGridPageRowUpdateAction(state$.value, moduleName, tableName, data, dependantFields));
                }

                if (tableName === TABLE_NAMES.BOOKING) {
                    const options = getBookingBudgetMessageOptionsSelector(state, moduleName)(data);

                    ewFieldChangeActions.push(
                        entityWindowUpdateMessages(moduleName, tableName, options)
                    );
                }

                return from(ewFieldChangeActions);
            })
        );
};

export const handleTimeAllocationReadonlyExplanation$ = (action$, state$, { apis }) => {
    return action$
        .ofType(actionTypes.ENTITY_WINDOW.DRIVEN_FIELD_ACTIONS.SET_TIME_ALLOCATION_READ_ONLY_EXPLANATION)
        .pipe(
            switchMap((action) => {
                const { moduleName } = action.payload;
                const uiEntity = getEntityWindowUIEntity(state$.value.entityWindow, moduleName);
                const tableName = getEntityWindowTableName(state$.value.entityWindow, moduleName);
                const alias = '';

                return validateEntityFields$(getValidateSubmitEntity(uiEntity, state$, moduleName), tableName, apis)
                    .pipe(
                        takeUntil(
                            action$.ofType(`${actionTypes.SET_DETAILS_PANE_SELECTED_TAB}_${alias}`)
                        )
                    );
            },
            (action, data) => { return { action, data }; }),
            map(result => {
                const { action, data } = result;
                const { moduleName } = action.payload;

                return updateTimeAllocationReadOnlyExplanation(moduleName, data, state$);
            })
        );
};

export const setTimeAllocationFTEExplanation$ = (action$, state$, { apis }) => {
    return action$
        .ofType(actionTypes.ENTITY_WINDOW.DRIVEN_FIELD_ACTIONS.SET_TIME_ALLOCATION_FTE_EXPLANATION)
        .pipe(
            filter(() => getIsMultipleAssigneesEnabled(state$.value)),
            switchMap((action) => {
                const state = state$.value;
                const { entityWindow } = state;
                const { moduleName, tableName = TABLE_NAMES.ROLEREQUEST } = action.payload;
                const uiEntity = getEntityWindowUIEntity(entityWindow, moduleName);

                return validateEntityFields$(getValidateSubmitEntity(uiEntity, state$, moduleName), tableName, apis);
            }, (action, data) => { return { action, data }; }),
            switchMap(result => {
                const state = state$.value;
                const { action, data } = result;
                const { moduleName, tableName = TABLE_NAMES.ROLEREQUEST } = action.payload;
                const translations = getTranslationsSelector(state, { sectionName: 'entityWindow' });

                const referenceDiaryTime = data[ROLEREQUEST_FIELDS.HOURS_PER_DAY] || HOURS_PER_DAY_FALLBACK_VALUE;
                const explanations = {
                    [ROLEREQUEST_FTE_FIELD_EXPLANATION_KEY]: populateStringTemplates(translations, { referenceDiaryTime }).rolerequestFTEFieldExplanation
                };

                return of(setFieldValueExplanations(moduleName, tableName, roleRequestFullTimeEquivalentField.name, explanations));
            })
        );
};

export const setBookingStatusExplanation$ = (action$, state$) => {
    return action$
        .ofType(actionTypes.ENTITY_WINDOW.DRIVEN_FIELD_ACTIONS.SET_BOOKING_STATUS_EXPLANATION)
        .pipe(
            switchMap((action) => {
                const { moduleName, fieldInfo } = action.payload;
                const { name } = fieldInfo;
                const state = state$.value;

                const translations = getTranslationsSelector(state, { sectionName: 'entityWindow' });
                const commonAliases = buildCommonEntityAliasesPlaceholdersSelector(state);
                const plannerPageAlias = getNavPageTitleSelector(state)(PLANNER_PAGE_ALIAS, PAGE_NAMES.PLANNER);

                const explanations = {
                    [bookingStatusSectionField.FIELD_VALUE_CONSTS.UNCONFIRMED]: populateStringTemplates(
                        translations,
                        { ...commonAliases, plannerPageAlias }
                    ).tableViewBookingStatusFieldExplanation
                };

                return of(setFieldValueExplanations(moduleName, TABLE_NAMES.BOOKING, name, explanations));
            })
        );
};

export const handleRejectReasonsExplanation$ = (action$, state$, { apis }) => {
    return action$.ofType(actionTypes.ENTITY_WINDOW.DRIVEN_FIELD_ACTIONS.SET_REJECT_REASONS_EXPLANATION).pipe(
        filter(({ payload: { moduleName } }) => {
            const window = createGetActiveWindowSelector()({
                entityWindow: state$.value.entityWindow.window[moduleName]
            });

            const roleStatus = getPageRoleRequestStatusDescriptionSelector(state$.value)(window.entity[ROLEREQUEST_FIELDS.STATUS_GUID]);

            return isRoleStatusRejected(roleStatus);
        }),
        switchMap((action) => {
            const { moduleName } = action.payload;
            const state = state$.value;
            const entityId = getActiveEntityID(state, moduleName);

            const requestFields = [
                { fieldName: `${TABLE_NAMES.ROLEREQUESTREJECTREASON}_${TABLE_NAMES.ROLEREQUEST}_guid` },
                {
                    fieldName: `${TABLE_NAMES.ROLEREQUESTREJECTREASON}_text`,
                    fieldAlias: `${TABLE_NAMES.ROLEREQUESTREJECTREASON}_text`
                },
                {
                    fieldName: `${TABLE_NAMES.ROLEREQUESTREJECTREASON}_guid`,
                    fieldAlias: `${TABLE_NAMES.ROLEREQUESTREJECTREASON}_guid`
                }
            ];

            const querySelection = {
                fields: requestFields,
                filter: {
                    filterGroupOperator: 'And',
                    filterLines: [{
                        field: `${TABLE_NAMES.ROLEREQUESTREJECTREASON}_${TABLE_NAMES.ROLEREQUEST}_guid`,
                        operator: 'Equals',
                        value: entityId
                    }]
                }
            };

            return apis[API_KEYS.TABLE_DATA].getTableData$(TABLE_NAMES.ROLEREQUESTREJECTREASON, querySelection);
        },
        (action, response) => { return { action, response }; }),
        map(result => {
            const { action, response } = result;
            const { moduleName } = action.payload;

            return updateRoleRejectReasonExplanation(moduleName, response[0] || {}, state$);
        })
    );
};

export const handleBarDateRangeChange$ = (action$, state$, { apis }) => {
    return action$
        .ofType(actionTypes.ENTITY_WINDOW.DRIVEN_FIELD_ACTIONS.UPDATE_BAR_DATE_RANGE_VALUE)
        .pipe(
            switchMap((action) => {
                const { moduleName, editedFieldName } = action.payload;
                const tableName = getEntityWindowTableName(state$.value.entityWindow, moduleName);
                const { fields, name: sectionName } = dateRangeSectionFieldByTableName[tableName];

                const isDateRangeField = (
                    editedFieldName === endDateSectionFieldByTableName[tableName]
                    || editedFieldName === startDateSectionFieldByTableName[tableName]
                    || editedFieldName === sectionName
                    || fields.some(field => field.name === editedFieldName)
                );

                let ewChangeActions = [setBookingWorkNonWorkDaysExplanation({ moduleName })];

                if (!isDateRangeField) {
                    ewChangeActions.push(entityWindowHighlightField(moduleName, dateRangeSectionFieldByTableName[tableName].name, ENTITY_WINDOW_CLEAR_HIGHLIGHT_DELAY));
                }

                return from(ewChangeActions);
            })
        );
};

const isSingleResource = (entity, tableName) => {
    return !(entity[`${tableName}_resource_guids`] && (entity[`${tableName}_resource_guids`].value != null && Array.isArray(entity[`${tableName}_resource_guids`].value) && entity[`${tableName}_resource_guids`].value.length != 1));
};

export const handleSetBookingWorkNonworkDaysExplanation$ = (action$, state$, { apis }) => {
    const entityCountPipe$ = action$.ofType(actionTypes.ENTITY_WINDOW.DRIVEN_FIELD_ACTIONS.SET_BOOKING_WORK_NONWORK_DAYS_EXPLANATION)
        .pipe(
            map(action => {
                const { moduleName } = action.payload;
                const state = state$.value;
                const { entityWindow } = state;
                const tableName = getEntityWindowTableName(entityWindow, moduleName);
                const uiEntity = getEntityWindowUIEntity(entityWindow, moduleName);
                const isSingle = isSingleResource(uiEntity, tableName);

                return { ...action, isSingle };
            })
        );

    const clearBookingWorkNonWorkDays$ = entityCountPipe$.pipe(
        filter(action => !action.isSingle),
        map(action => {
            const { moduleName } = action.payload;

            return clearBookingWorkNonWorkDaysExplanation({ moduleName });
        })
    );

    const setBookingWorkNonWorkDays$ = entityCountPipe$.pipe(
        filter(action => action.isSingle),
        switchMap((action) => {
            const { moduleName } = action.payload;
            const alias = '';
            const uiEntity = getEntityWindowUIEntity(state$.value.entityWindow, moduleName);
            const tableName = getEntityWindowTableName(state$.value.entityWindow, moduleName);

            return validateEntityFields$(getValidateSubmitEntity(uiEntity, state$, moduleName), tableName, apis)
                .pipe(
                    takeUntil(
                        action$.ofType(`${actionTypes.SET_DETAILS_PANE_SELECTED_TAB}_${alias}`)
                    )
                );
        },
        (action, data) => { return { action, data }; }),
        switchMap(result => {
            const { action, data } = result;
            const { moduleName } = action.payload;
            const tableName = getEntityWindowTableName(state$.value.entityWindow, moduleName);
            const { name } = dateRangeSectionFieldByTableName[tableName];
            const explanations = {
                [UNSPECIFIED_FIELD_VALUE_EXPLANATION_KEY]: getDateRangeReadOnlyExplanation(data, state$, tableName),
                [ACTIONABLE_FIELD_VALUE_EXPLANATION_KEY]: getRepeatBookingExplanation(state$.value)
            };

            return of(
                setFieldValueExplanations(moduleName, tableName, name, explanations)
            );
        })
    );

    return merge(
        setBookingWorkNonWorkDays$,
        clearBookingWorkNonWorkDays$
    );
};

const handleClearBookingWorkNonworkDaysExplanation$ = (action$, state$) => {
    return action$
        .ofType(actionTypes.ENTITY_WINDOW.DRIVEN_FIELD_ACTIONS.CLEAR_BOOKING_WORK_NONWORK_DAYS_EXPLANATION)
        .pipe(
            filter(({ payload: { moduleName } }) => {
                const entityWindow = state$.value.entityWindow;
                const tableName = getEntityWindowTableName(entityWindow, moduleName);

                return hasFieldValueExplanation(entityWindow.fieldValueExplanations[moduleName], tableName, dateRangeSectionFieldByTableName[tableName].name);
            }),
            map(({ payload: { moduleName } }) => {
                const tableName = getEntityWindowTableName(state$.value.entityWindow, moduleName);

                return clearFieldValueExplanations(
                    moduleName,
                    getEntityWindowTableName(state$.value.entityWindow, moduleName),
                    dateRangeSectionFieldByTableName[tableName].name
                );
            })
        );
};

export const getResourceDiariesSelection = (entityWindow, getFieldInfo) => {
    const { entity, tableName } = entityWindow;

    const selectionBuilder = new TableSelectionBuilder(TABLE_NAMES.RESOURCE, getFieldInfo)
        .addFields([
            { fieldName: FILTER_FIELD_NAMES.RESOURCE_GUID, fieldAlias: 'id' },
            { fieldName: DIARY_GROUP, fieldAlias: DIARY_GROUP.toLowerCase() },
            { fieldName: RESOURCE_USERSTATUS, fieldAlias: RESOURCE_USERSTATUS }
        ]);

    let resourceIds = [];

    if (entity[`${tableName}_${RESOURCE_GUID}`]) {
        resourceIds = [entity[`${tableName}_${RESOURCE_GUID}`]];
    } else if (entity[`${tableName}_${RESOURCE_GUIDS}`] && !entity[`${tableName}_${RESOURCE_GUIDS}`].includes(UNASSIGNED_BOOKINGS_RESOURCE.RESOURCE_GUID)) {
        resourceIds = entity[`${tableName}_${RESOURCE_GUIDS}`];
    }

    //We append the filter only for date fields, depending on resource field. For now we have those only in booking and rolerequest tables
    if (resourceIds && (tableName === TABLE_NAMES.BOOKING || tableName === TABLE_NAMES.ROLEREQUEST)) {
        selectionBuilder.addFilter({ field: RESOURCE_GUID, operator: OPERATORS.DB_OPERATORS.CONTAINS, value: resourceIds });
    }

    return selectionBuilder.getSelection();
};

export const getResourceDiaries$ = (action$, state$, { apis }) => {
    const actionsOfInterest = [
        `${actionTypes.ENTITY_WINDOW.GET_DYNAMIC_CONFIG}_${ENTITY_WINDOW_MODULES.PLANNER_PAGE_MODAL}`,
        `${actionTypes.ENTITY_WINDOW.GET_DYNAMIC_CONFIG}_${ENTITY_WINDOW_MODULES.PLANNER_PAGE_DETAILS_PANE}`,
        `${actionTypes.ENTITY_WINDOW.GET_DYNAMIC_CONFIG}_${ENTITY_WINDOW_MODULES.PLANNER_PAGE_MODAL_SIMPLIFIED}`,
        `${actionTypes.ENTITY_WINDOW.GET_DYNAMIC_CONFIG}_${ENTITY_WINDOW_MODULES.PLANNER_PAGE_BATCH_MODAL}`,
        `${actionTypes.ENTITY_WINDOW.GET_DYNAMIC_CONFIG}_${ENTITY_WINDOW_MODULES.PLANNER_PAGE_BATCH_DETAILS_PANE}`,
        `${actionTypes.ENTITY_WINDOW.GET_DYNAMIC_CONFIG}_${ENTITY_WINDOW_MODULES.PLANNER_PAGE_ASSIGNEES_BUDGET_MODAL}`,
        `${actionTypes.ENTITY_WINDOW.GET_DYNAMIC_CONFIG}_${ENTITY_WINDOW_MODULES.ROLE_REQUEST_FORM}`,
        `${actionTypes.ENTITY_WINDOW.GET_DYNAMIC_CONFIG}_${ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_MODAL}`,
        `${actionTypes.ENTITY_WINDOW.GET_DYNAMIC_CONFIG}_${ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_BATCH_MODAL}`,
        `${actionTypes.ENTITY_WINDOW.GET_DYNAMIC_CONFIG}_${ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_MODAL_SIMPLIFIED}`,
        `${actionTypes.ENTITY_WINDOW.GET_DYNAMIC_CONFIG}_${ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_DETAILS_PANE}`,
        `${actionTypes.ENTITY_WINDOW.GET_DYNAMIC_CONFIG}_${ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_BATCH_DETAILS_PANE}`,
        `${actionTypes.ENTITY_WINDOW.GET_DYNAMIC_CONFIG}_${ENTITY_WINDOW_MODULES.ROLE_INBOX_ASSIGNEES_BUDGET_MODAL}`,
        `${actionTypes.ENTITY_WINDOW.GET_DYNAMIC_CONFIG}_${ENTITY_WINDOW_MODULES.NOTIFICATION_PAGE_MODAL}`,
        `${actionTypes.ENTITY_WINDOW.GET_DYNAMIC_CONFIG}_${ENTITY_WINDOW_MODULES.PROFILE_PAGE_MODAL}`,
        `${actionTypes.ENTITY_WINDOW.GET_DYNAMIC_CONFIG}_${ENTITY_WINDOW_MODULES.TABLE_VIEW_MODAL}`,
        `${actionTypes.ENTITY_WINDOW.GET_DYNAMIC_CONFIG}_${ENTITY_WINDOW_MODULES.TABLE_VIEW_MODAL_SIMPLIFIED}`
    ];

    const loaded$ = action$
        .ofType(...actionsOfInterest)
        .pipe(
            mergeMap(
                (action) => {
                    const { moduleName } = action.payload;
                    const { entityWindow } = state$.value;
                    const getActiveWindowSelector = createGetActiveWindowSelector();
                    const activeEntityWindow = getActiveWindowSelector({ entityWindow: entityWindow.window[moduleName] });
                    const getFieldInfo = getFieldInfoSelector(state$.value);

                    const selection = getResourceDiariesSelection(activeEntityWindow, getFieldInfo);

                    return apis['tableData'].getTableData$(TABLE_NAMES.RESOURCE, selection);
                },
                (action, result) => {
                    const { moduleName } = action.payload;
                    const entityWindow = state$.value.entityWindow;
                    const tableName = getEntityWindowTableName(entityWindow, moduleName);
                    const dynamicConfiguration = getEntityDynamicConfig(moduleName, tableName, state$.value, { dirayGroups: result });

                    return injectDynamicConfiguration({ dynamicConfiguration, moduleName });
                }
            )
        );

    return loaded$;
};

const handleUpdateResourcesGuidsRoleCreation$ = (action$, state$) => {
    const interceptEWOpen$ = action$
        .ofType(actionTypes.ENTITY_WINDOW.DRIVEN_FIELD_ACTIONS.UPDATE_ROLE_RESOURCE_MESSAGE_WARNING)
        .pipe(
            map((action) => {
                const state = state$.value;
                const { moduleName } = action.payload;
                const entityWindow = state.entityWindow.window[moduleName];
                const { entity } = entityWindow.isBatch && entityWindow.activeEntity
                    ? entityWindow.windows[entityWindow.activeEntity]
                    : entityWindow;
                const resourcesIds = [entity[ROLEREQUEST_FIELDS.RESOURCE_GUID]];

                return {
                    state,
                    resourcesIds,
                    moduleName
                };
            })
        );

    const interceptFieldChange$ = action$
        .ofType(actionTypes.ENTITY_WINDOW.DRIVEN_FIELD_ACTIONS.ROLE_CREATION_UPDATE_RESOURCE_GUIDS)
        .pipe(
            filter((action) => {
                const { moduleName } = action.payload;

                return getEntityWindowOperation(state$.value, moduleName) == ENTITY_WINDOW_OPERATIONS.CREATE;
            }),
            map(({ payload }) => {
                const state = state$.value;
                const { fieldValue, moduleName } = payload;
                const resourcesIds = Array.isArray(fieldValue) ? fieldValue : [fieldValue.id];

                return {
                    state,
                    resourcesIds,
                    moduleName
                };
            })
        );

    const messageActions =
        merge(interceptEWOpen$, interceptFieldChange$)
            .pipe(
                switchMap(({ state, resourcesIds, moduleName }) => {
                    const { entity, entityId = null } = createGetActiveWindowSelector()({
                        entityWindow: state.entityWindow.window[moduleName]
                    });
                    const isCriteriaRole = getIsCriteriaRole(entity);

                    const staticMessages = getEntityWindowMessagesSelector(state)(moduleName);
                    const hasValidResource = roleResourcesReadyForBookingRequest(resourcesIds);
                    const getAccessibleEntitiesIds = getAccessibleEntitiesIdsSelector(state);

                    const insufficientCreateBookingRequestFNA = roleRequestBookingFunctionalAccessExcludeCondition.conditionCheck({ getAccessibleEntitiesIds, entityId });
                    const hasUnassignedResource = roleResourcesHasUnassignedResource(resourcesIds);
                    const operation = ENTITY_WINDOW_OPERATIONS.CREATE;
                    const hasHiddenRequirements = entityId && getHasHiddenRequirementsSelector(state, entityId, CRITERIA_SECTIONS.MUST_MEET);

                    const options = {
                        operation,
                        hasValidResource,
                        staticMessages,
                        hasUnassignedResource,
                        insufficientCreateBookingRequestFNA,
                        isCriteriaRole,
                        entityId,
                        hasHiddenRequirements
                    };

                    return of(
                        entityWindowSetMessages(moduleName, TABLE_NAMES.ROLEREQUEST, options)
                    );
                })
            );

    return messageActions;
};

const handleClearRolerequestGroupFieldValue$ = (action$, state$) => {
    return action$
        .ofType(actionTypes.ENTITY_WINDOW.DRIVEN_FIELD_ACTIONS.CLEAR_ROLEREQUEST_GROUP_FIELD_VALUE)
        .pipe(
            switchMap(({ payload }) => {
                const state = state$.value;
                const { moduleName, entityId = '', entityIds = [] } = payload;

                const getFieldInfo = getFieldInfoSelector(state);
                const fieldInfo = getFieldInfo(roleRequestGroupGuidField.table, roleRequestGroupGuidField.name);

                const windowModal = isWindowModal(moduleName);
                const batchModal = isBatchWindowModal(moduleName);

                const changeFieldAction = windowModal
                    ? batchModal
                        ? entityWindowFieldChangedForAllEntities
                        : entityWindowFieldChanged
                    : entityWindowFieldChangedContextually;

                return of(changeFieldAction(moduleName, fieldInfo, '', batchModal ? entityIds : entityId));
            })
        );
};

const handleUpdateBudgetSection$ = (action$, state$) => {
    return action$
        .ofType(
            actionTypes.ENTITY_WINDOW.DRIVEN_FIELD_ACTIONS.UPDATE_BUDGET_SECTION
        )
        .pipe(
            switchMap(({ payload }) => {
                let action = empty();
                const { tableName, moduleName } = payload;
                const { entity } = createGetActiveWindowSelector()({
                    entityWindow: state$.value.entityWindow.window[moduleName]
                });

                if (getEntityHasMultipleResources(tableName, entity)) {
                    const isBudgetSectionContentHidden = isEntityWindowSectionContentHidden(
                        getEntityWindowSectionByKey(state$.value, moduleName, tableName, ENTITY_WINDOW_SECTION_KEYS.BUDGET)
                    );

                    action = of(entityWindowHideSectionContent(moduleName, tableName, ENTITY_WINDOW_SECTION_KEYS.BUDGET, !isBudgetSectionContentHidden));
                }

                return action;
            })
        );
};

const handleRolerequestEntityWindowsMessageUpdate$ = (action$, state$) => {
    return action$
        .ofType(
            actionTypes.ENTITY_WINDOW.DRIVEN_FIELD_ACTIONS.UPDATE_ROLE_ENTITY_WINDOW_SECTION_MESSAGES
        )
        .pipe(
            switchMap(({ payload }) => {
                return from(updateBudgetSectionMessages(state$.value, payload, payload.moduleName));
            })
        );
};

const handleSectionMessagesUpdate$ = (action$, state$) => {
    return action$
        .ofType(
            actionTypes.ENTITY_WINDOW.DRIVEN_FIELD_ACTIONS.UPDATE_ENTITY_WINDOW_SECTION_MESSAGES
        )
        .pipe(
            switchMap(({ payload }) => {
                const state = state$.value;
                let result = empty();
                const { tableName, moduleName } = payload;
                const operation = getEntityWindowOperation(state, moduleName);

                let currentEntity = getEntityWindowEntity(state.entityWindow, moduleName);

                if (getEntityHasMultipleResources(tableName, currentEntity)) {
                    result = of(entityWindowSetMessages(moduleName, tableName, { operation, bookingHasMultipleResources: true }));
                } else if (tableName === TABLE_NAMES.ROLEREQUEST) {
                    result = of(updateRolerequestEntityWindowSectionMessages(payload));
                }

                return result;
            })
        );
};

export const createHandleUpdateFieldOnContextualEdit = (fieldName, fieldCalcFunc) => {
    return (action$, state$) => {
        return action$
            .ofType(
                `${actionTypes.ENTITY_WINDOW.DRIVEN_FIELD_ACTIONS.UPDATE_FIELD_ON_CONTEXTUAL_EDIT}_${fieldName}`
            )
            .pipe(
                switchMap((action) => {
                    const state = state$.value;
                    const { moduleName } = action.payload;
                    const uiEntity = getEntityWindowUIEntity(state.entityWindow, moduleName);
                    const newValue = fieldCalcFunc(uiEntity);

                    return of(
                        updateFieldValue(
                            moduleName,
                            newValue,
                            fieldName,
                            ENTITY_WINDOW_OPERATIONS.CONTEXTUAL_EDIT
                        )
                    );
                })
            );
    };
};

export const handleUpdateBudgetSectionVisibility$ = (action$, state$) => {
    return action$
        .ofType(
            actionTypes.ENTITY_WINDOW.DRIVEN_FIELD_ACTIONS.UPDATE_BUDGET_SECTION_FIELDS_VISIBILITY
        )
        .pipe(
            switchMap(({ payload }) => {
                const { moduleName, operation } = payload;
                const { activeEntity, windows, tableName, isBatch } = state$.value.entityWindow.window[moduleName];
                const showReadOnlybudgetFields = (
                    operation === ENTITY_WINDOW_OPERATIONS.READ
                    || operation === ENTITY_WINDOW_OPERATIONS.CONTEXTUAL_EDIT
                    || (operation && operation.default === ENTITY_WINDOW_OPERATIONS.CONTEXTUAL_EDIT)
                    || (!!isBatch && windows[activeEntity].operation === ENTITY_WINDOW_OPERATIONS.READ)
                );

                return of(changeReadOnlyFieldsVisibility(moduleName, ENTITY_WINDOW_SECTION_KEYS.BUDGET, tableName, showReadOnlybudgetFields));
            })
        );
};

const ENTITY_WINDOW_MODULE_TO_TABLE_DATA_NAME_MAP = {
    [ENTITY_WINDOW_MODULES.PLANNER_PAGE_MODAL]: `${PLANNER_PAGE_ALIAS}_${PLANNER_TABLE_DATAS_SUFFIX}`,
    [ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_MODAL]: ROLE_INBOX_PAGE_TABLE_DATA_ALIAS,
    [ENTITY_WINDOW_MODULES.TABLE_VIEW_MODAL]: `${TABLE_VIEW_PAGE_ALIAS}_${PLANNER_TABLE_DATAS_SUFFIX}`
};

const handleBarMultipleResourceChanged$ = (action$, state$) => {
    return action$
        .ofType(
            actionTypes.ENTITY_WINDOW.DRIVEN_FIELD_ACTIONS.BAR_MULTIPLE_RESOURCES_CHANGED
        )
        .pipe(
            switchMap(({ payload }) => {
                const { fieldInfo, fieldValue = [], moduleName } = payload;
                const { items, actualFieldName, table } = fieldInfo.value;
                const state = state$.value;
                const tableName = getEntityWindowTableName(state.entityWindow, moduleName);
                const getFieldInfo = getFieldInfoSelector(state);

                const actualFieldInfo = getFieldInfo(table, actualFieldName);
                const primaryFieldTableName = getFieldTableName(actualFieldInfo, table);
                const tableDatas = items.map(item => getEntityOptionFields(item, primaryFieldTableName));

                let actions = [
                    ...getTableDatasLoadedActions(ENTITY_WINDOW_MODULE_TO_TABLE_DATA_NAME_MAP[moduleName], { [primaryFieldTableName]: tableDatas })
                ];

                const isBudgetSectionContentHidden = isEntityWindowSectionContentHidden(
                    getEntityWindowSectionByKey(state, moduleName, table, ENTITY_WINDOW_SECTION_KEYS.BUDGET)
                );

                const resourcesCount = fieldValue.length;

                const shouldHideBudgetSectionContent = resourcesCount > 1 && !isBudgetSectionContentHidden;
                const operation = getEntityWindowOperation(state, moduleName);

                actions.push(
                    entityWindowHideSectionContent(moduleName, table, ENTITY_WINDOW_SECTION_KEYS.BUDGET, shouldHideBudgetSectionContent),
                    entityWindowSetMessages(moduleName, table, { operation, bookingHasMultipleResources: resourcesCount > 1 })
                );
                let reourceFieldValue = { id: null };

                if (resourcesCount == 1) {
                    reourceFieldValue = { id: fieldValue[0] === UNASSIGNED_BOOKINGS_RESOURCE.RESOURCE_GUID ? null : fieldValue[0] };

                    actions.push(
                        loadResourceChargeRateCurrentValue({ moduleName, entity: { booking_resource_guid: reourceFieldValue.id } }),
                        updateBookingChargeRateValues({ moduleName })
                    );
                }

                actions.push(
                    entityWindowDrivenFieldChanged(moduleName, getFieldInfo(table, resourceSectionFieldByTableName[tableName].name), reourceFieldValue),
                    updateBookingTimeAllocationValues({ moduleName, isContextuallyEditing: false })
                );

                return from(actions);
            })
        );
};

const loadBookingMultipleResourcesAvatars$ = (action$, state$, { apis }) => {
    return action$
        .ofType(actionTypes.ENTITY_WINDOW.DRIVEN_FIELD_ACTIONS.LOAD_BOOKING_MULTIPLE_RESOURCES_AVATARS)
        .pipe(
            mergeMap(({ payload }) => {
                const { tableName } = payload;
                const avatars = state$.value.avatars;
                const avatarSize = AVATAR_SIZES.SMALL;
                const ids = payload.entity[`${tableName}_resource_guids`] || [];

                const loadAvatarIds = ids
                    .filter(id => id !== UNASSIGNED_BOOKINGS_RESOURCE.RESOURCE_GUID && resourceAvatarLoaded(avatars, id, avatarSize.label));

                return of(loadAvatars(loadAvatarIds, avatarSize));
            })
        );
};

export const handleBarResourceDescriptionReadonlyExplanation$ = (action$, state$, { apis }) => {
    const getLinkedDataWrappedSelector = createGetLinkedDataWrappedSelector();

    return action$
        .ofType(actionTypes.ENTITY_WINDOW.DRIVEN_FIELD_ACTIONS.SET_BAR_RESOURCE_DESCRIPTION_EXPLANATION)
        .pipe(
            switchMap((action) => {
                const { moduleName } = action.payload;
                const uiEntity = getEntityWindowUIEntity(state$.value.entityWindow, moduleName);
                const shouldValidate = uiEntity[BOOKING_START].value != null;

                let validationResult = of({});

                if (shouldValidate) {
                    const tableName = getEntityWindowTableName(state$.value.entityWindow, moduleName);

                    validationResult = validateEntityFields$(getValidateSubmitEntity(uiEntity, state$, moduleName), tableName, apis);
                }

                return validationResult.pipe(map((data) => { return { action, data }; }));
            }),
            switchMap((result) => {
                const { action, data } = result;
                const { moduleName } = action.payload;
                const entityWindowTableName = getEntityWindowTableName(state$.value.entityWindow, moduleName);
                const { table: tableName, name } = resourceSectionFieldByTableName[entityWindowTableName];
                const { entityWindow } = state$.value;

                const getLinkedData = getLinkedDataWrappedSelector({
                    tableStructure: getTableStructure(state$.value),
                    primaryTableName: entityWindow.window[moduleName].tableName,
                    autoComplete: entityWindow.autoComplete[moduleName],
                    dataCollections: getPageTableDatasSelector(state$.value)
                });

                const options = getBookingBudgetMessageOptionsSelector(state$.value, moduleName)(data);
                const translations = getTranslationsSelector(state$.value, { sectionName: 'entityWindow' });
                const commonEntityAliases = buildCommonEntityAliasesPlaceholdersSelector(state$.value);
                const currentEntityTemplate = entityWindow.settings[moduleName].entityTemplates[entityWindowTableName];
                const explanation = getBookingResourceDescriptionReadOnlyExplanation(data, getLinkedData, translations, commonEntityAliases, currentEntityTemplate);
                const explanations = {
                    [UNSPECIFIED_FIELD_VALUE_EXPLANATION_KEY]: explanation
                };

                return from([
                    setFieldValueExplanations(moduleName, tableName, name, explanations),
                    entityWindowUpdateMessages(moduleName, tableName, options)
                ]);
            })
        );
};

const shouldValidateChargeRateValues = (state, tableName, moduleName) => {
    let shouldValidate = true;
    const entity = getEntityWindowEntity(state.entityWindow, moduleName) || {};

    if (tableName === TABLE_NAMES.ROLEREQUEST && getIsCriteriaRole(entity)) {
        const statusDescription = getPageRoleRequestStatusDescriptionSelector(state)(entity[ROLEREQUEST_FIELDS.STATUS_GUID]);

        shouldValidate = statusDescription !== ROLE_ITEM_STATUS_KEYS.REQUESTED;
    }

    return shouldValidate;
};

export const handleBarChargeRateValues$ = (action$, state$, { apis }) => {
    return action$
        .ofType(actionTypes.ENTITY_WINDOW.DRIVEN_FIELD_ACTIONS.UPDATE_BAR_CHARGE_RATE_VALUES)
        .pipe(
            switchMap((action) => {
                const state = state$.value;
                const { moduleName } = action.payload;
                const tableName = getEntityWindowTableName(state.entityWindow, moduleName);

                let validationResult = of({});

                if (shouldValidateChargeRateValues(state, tableName, moduleName)) {
                    const uiEntity = getEntityWindowUIEntity(state.entityWindow, moduleName);
                    const entity = getEntityWindowEntity(state.entityWindow, moduleName);

                    const { dependantFields } = getFieldByTableName(tableName, entity, chargeModeSectionFieldByTableName);

                    const getFieldInfoWrapped = (tableName, fieldName) => getFieldInfo(getTableStructure(state), tableName, fieldName);
                    const requestPayload = buildValidateRichFieldsPayload(getValidateSubmitEntity(uiEntity, state$, moduleName), dependantFields, getFieldInfoWrapped);

                    validationResult = validateEntityRichFields$(requestPayload, tableName, apis);
                }

                return validationResult.pipe(map((data) => { return { action, data }; }));
            }),
            switchMap(result => {
                const state = state$.value;
                const { action, data } = result;
                const { moduleName, editedFieldName } = action.payload;

                const tableName = getEntityWindowTableName(state.entityWindow, moduleName);
                const entity = getEntityWindowEntity(state.entityWindow, moduleName);

                const { dependantFields } = getFieldByTableName(tableName, entity, chargeModeSectionFieldByTableName);

                const budgetSectionKey = getBudgetSectionKey(tableName, entity);
                const budgetSection = state.entityWindow.settings[moduleName].sections[tableName]
                    .filter((section) => section.key === budgetSectionKey)[0] || {};

                const showReadOnlyBudget = budgetSection.showReadOnlyFields === true;

                if (isEmptyObject(data)) {
                    return from([]);
                }

                const ewFieldChangeActions = dependantFields
                    .filter((field) => showReadOnlyBudget !== true
                        || (
                            field.name !== getFieldByTableName(tableName, entity, revenuePerHourFieldByTableName)
                            && field.name !== getFieldByTableName(tableName, entity, costPerHourFieldByTableName)
                        ))
                    .map((field) => {
                        const fieldInfo = getFieldInfo(getTableStructure(state$.value), field.table, field.name) || {};
                        let fieldValue = data[field.name];

                        if (fieldInfo.appInternalField) {
                            fieldValue = getAppFieldValue(field.name, data, tableName);
                        }

                        return entityWindowDrivenFieldChanged(moduleName, fieldInfo, fieldValue);
                    });

                const gridUpdateFields = [ROLEREQUEST_DATE_RANGE, ROLEREQUEST_TIME_ALLOCATION];

                //TODO: this should be double checked when adding estimates to role inbox grid
                if (isRoleInboxPageDp(moduleName) && gridUpdateFields.includes(editedFieldName)) {
                    const gridDependantFields = [
                        ROLEREQUEST_FIELDS.TOTALCOST,
                        ROLEREQUEST_FIELDS.TOTALPROFIT,
                        ROLEREQUEST_FIELDS.TOTALREVENUE,
                        ROLEREQUEST_FIELDS.REVENUE_PER_HOUR,
                        ROLEREQUEST_FIELDS.COST_PER_HOUR,
                        ROLEREQUEST_FIELDS.DIARY_DAYS,
                        ROLEREQUEST_FIELDS.DIARY_HOURS
                    ];

                    ewFieldChangeActions.push(...getDataGridPageRowUpdateAction(state$.value, moduleName, tableName, data, gridDependantFields));
                }

                if (tableName === TABLE_NAMES.BOOKING) {
                    const options = getBookingBudgetMessageOptionsSelector(state$.value, moduleName)(data);

                    ewFieldChangeActions.push(
                        entityWindowUpdateMessages(moduleName, tableName, options)
                    );
                }

                return from(ewFieldChangeActions);
            })
        );
};

const handleLoadResourceChargeRateCurrentValue$ = (action$, state$, { apis }) => {
    return action$
        .ofType(actionTypes.ENTITY_WINDOW.DRIVEN_FIELD_ACTIONS.LOAD_RESOURCE_CHARGE_RATE_CURRENT_VALUE)
        .pipe(
            switchMap(
                (action) => {
                    const state = state$.value;
                    const { moduleName, entity } = action.payload;
                    const entityWindowEntity = getEntityWindowEntity(state.entityWindow, moduleName);

                    const { activeEntity, windows, tableName, isBatch } = state.entityWindow.window[moduleName];
                    const currentEntity = isBatch
                        ? {
                            ...windows[activeEntity].entity,
                            [`${tableName}_guid`]: activeEntity
                        }
                        : entity;
                    let resourceGuid = (currentEntity || entityWindowEntity)[`${tableName}_${TABLE_NAMES.RESOURCE}_guid`];

                    if (getIsCriteriaRole(currentEntity)) {
                        resourceGuid = getCriteriaRoleAssigneesIdsSelector(state)(currentEntity);
                    }

                    let selection = {
                        fields: [
                            { fieldName: RESOURCE_GUID },
                            { fieldName: RESOURCE_DESCRIPTION },
                            {
                                fieldName: CHARGERATE_CURRENT_VALUE_FIELDNAME,
                                fieldAlias: CHARGERATE_CURRENT_VALUE_FIELDNAME
                            }
                        ],
                        filter: {
                            filterGroupOperator: 'And',
                            filterLines: []
                        }
                    };

                    if (resourceGuid) {
                        selection.filter.filterLines.push({
                            field: `${TABLE_NAMES.RESOURCE}_guid`,
                            operator: Array.isArray(resourceGuid) ? 'Contains' : 'Equals',
                            value: resourceGuid
                        });

                        return apis[TABLE_DATA].getTableData$(TABLE_NAMES.RESOURCE, selection);
                    } else {
                        return of([]);
                    }
                },
                (action, result) => [action, result]
            ),
            mergeMap(([action, result]) => {
                const { moduleName } = action.payload;
                const state = state$.value;

                const currentPage = getCurrentPageAliasSelector(state);
                const tableDataAlias = getTableDatasAlias(currentPage);

                let chain = [
                    loadMoreTableDataSuccess(
                        tableDataAlias,
                        {
                            tableDataGuid: TABLE_NAMES.RESOURCE,
                            tableNames: [TABLE_NAMES.RESOURCE]
                        },
                        unionBy(result, RESOURCE_GUID)
                    )
                ];

                const { noRateSetLabel } = getTranslationsSelector(state, { sectionName: 'common' });
                const fieldValue = Array.isArray(result) && result.length > 0
                    ? result.map(resource => resource[CHARGERATE_CURRENT_VALUE_FIELDNAME] || noRateSetLabel)
                        .join(', ')
                    : null;

                chain.push(entityWindowDrivenFieldChanged(moduleName, { name: CHARGERATE_CURRENT_VALUE_FIELDNAME }, fieldValue));

                return from(chain);
            })
        );
};

const getResourceTinyAvatar$ = (action$, state$, { apis }) => {
    return action$
        .ofType(actionTypes.ENTITY_WINDOW.DRIVEN_FIELD_ACTIONS.GET_RESOURCE_TINY_AVATAR)
        .pipe(
            filter(({ payload }) => {
                const { fieldValue } = payload;
                const fieldIsPopulated = Object.keys(fieldValue).length !== 0 && fieldValue.id;
                const avatars = state$.value.avatars;

                return fieldIsPopulated && !avatarImageLoaded(avatars, fieldValue.id, AVATAR_SIZES.TINY.label);
            }),
            switchMap(({ payload }) => {
                const { fieldValue } = payload;

                return loadSingleAvatar$(fieldValue.id, AVATAR_SIZES.TINY, apis.avatars);
            }),
            map(res => loadAvatarSuccess(null, res, res.avatar))
        );
};

const handleUpdateResourcesGuids$ = (action$, state$) => {
    return action$
        .ofType(actionTypes.ENTITY_WINDOW.DRIVEN_FIELD_ACTIONS.UPDATE_RESOURCE_GUIDS)
        .pipe(
            switchMap(({ payload }) => {
                const { fieldValue, moduleName, fieldInfo } = payload;
                const tableName = getEntityWindowTableName(state$.value.entityWindow, moduleName);

                let actions = [];

                if (fieldInfo && fieldInfo.name === resourceSectionFieldByTableName[tableName].name) {
                    const resourcesValue = fieldValue.id ? [fieldValue.id] : null;

                    actions.push(
                        entityWindowDrivenFieldChanged(moduleName, { name: resourceGuidsFieldByTableName[tableName] }, resourcesValue)
                    );
                }

                return from(actions);
            })
        );
};

const getResourcesCount$ = (action$, state$) => {
    const actionsOfInterest = [
        `${actionTypes.ENTITY_WINDOW.GET_DYNAMIC_CONFIG}_${ENTITY_WINDOW_MODULES.PLANNER_PAGE_MODAL}`,
        `${actionTypes.ENTITY_WINDOW.GET_DYNAMIC_CONFIG}_${ENTITY_WINDOW_MODULES.TABLE_VIEW_MODAL}`,
        `${actionTypes.ENTITY_WINDOW.GET_DYNAMIC_CONFIG}_${ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_MODAL}`
    ];

    return action$
        .ofType(...actionsOfInterest)
        .pipe(
            switchMap(({ payload }) => {
                const { moduleName } = payload;
                const { entityWindow } = state$.value;
                const tableName = getEntityWindowTableName(state$.value.entityWindow, moduleName);

                const uiEntity = getEntityWindowUIEntity(entityWindow, moduleName);
                const resourcesCount = uiEntity && uiEntity[`${tableName}_resource_guids`] && (uiEntity[`${tableName}_resource_guids`].value || []).length;

                const dynamicConfiguration = {
                    [moduleName]: {
                        resourcesCount
                    }
                };

                return of(injectDynamicConfiguration({ dynamicConfiguration, moduleName }));
            })
        );
};

const handleGetEntityAuditData$ = (action$, state$) => {
    return action$
        .ofType(actionTypes.ENTITY_WINDOW.DRIVEN_FIELD_ACTIONS.GET_ENTITY_AUDIT_DATA)
        .pipe(
            filter(({ payload }) => {
                const { moduleName } = payload;
                const { value: { entityWindow } } = state$;
                const { activeTab, activeEntity } = entityWindow.window[moduleName];

                return activeTab === ENTITY_WINDOW_TAB_KEYS.HISTORY && activeEntity !== EDIT_ALL_ENTITY_ID;
            }),
            switchMap(({ payload }) => {
                const { moduleName } = payload;
                const { value: { entityWindow } } = state$;
                const { tableName, activeEntity } = entityWindow.window[moduleName];

                return of(loadAudit(tableName, activeEntity));
            })
        );
};

const handleUpdateFieldTableData$ = (action$, state$) => {
    return action$
        .ofType(actionTypes.ENTITY_WINDOW.DRIVEN_FIELD_ACTIONS.UPDATE_FIELD_TABLE_DATA)
        .pipe(
            switchMap(({ payload }) => {
                return of(loadFieldValues(payload));
            })
        );
};

const handleUpdatePageParamsData$ = (action$, state$) => {
    return action$
        .ofType(actionTypes.ENTITY_WINDOW.DRIVEN_FIELD_ACTIONS.UPDATE_PAGE_PARAMS)
        .pipe(
            switchMap(({ payload }) => {
                const currentPage = getCurrentPageAliasSelector(state$.value);

                return of(updatePageParams(currentPage, payload.params));
            })
        );
};

export const validateActiveEntity$ = (action$, state$) => {
    return action$
        .ofType(actionTypes.ENTITY_WINDOW.DRIVEN_FIELD_ACTIONS.VALIDATE)
        .pipe(
            switchMap((action) => {
                const { moduleName, tableName } = action.payload;
                const { activeEntity } = state$.value.entityWindow.window[moduleName];

                return of(validateMultipleEntities({ tableName, moduleName, entityIds: [activeEntity] }));
            })
        );
};

export const validateMultipleEntitiesEpic$ = (action$, state$) => {
    return action$
        .ofType(actionTypes.ENTITY_WINDOW.DRIVEN_FIELD_ACTIONS.VALIDATE_MULTIPLE_ENTITIES)
        .pipe(
            filter(({ errorsFound }) => errorsFound !== false),
            switchMap((action) => {
                const { moduleName, entityIds } = action.payload;
                const window = state$.value.entityWindow.window[moduleName];
                const validateIds = entityIds || window.batchIds;

                const patchData = validateIds.reduce((accumulator, id) => {
                    const activeEntity = window.windows[id];
                    const tableData = activeEntity.entity || activeEntity;
                    const shouldValidate = getIsRoleEditable(state$.value, tableData[ROLEREQUEST_FIELDS.STATUS_GUID], id);

                    if (shouldValidate) {
                        accumulator.push({ tableDataEntryGuid: id, tableData });
                    }

                    return accumulator;
                }, []);

                const followUpAction = {
                    type: action.type,
                    payload: {
                        ...action.payload,
                        patchData
                    }
                };

                return patchData.length > 0 ? formValidationMapAction(state$, followUpAction) : empty();
            }),
            switchMap((validateErrors) => {
                let errorActions = [];

                if (validateErrors && Array.isArray(validateErrors)) {
                    errorActions = validateErrors.filter(a => a !== undefined);
                }

                return from(errorActions);
            })
        );
};

const getSystemFieldNames = (tableName) => {
    return {
        updatedByGuid: `${tableName}_updatedby_resource_guid`,
        updatedByValue: `${tableName}_updatedby_resource_guid.resource_description`,
        updatedOn: `${tableName}_updatedon`
    };
};

const buildSystemInfoFieldsStructure = (fields = {}, { updatedByGuid, updatedByValue, updatedOn }) => {
    return {
        [updatedByGuid]: {
            id: fields[updatedByGuid],
            value: fields[updatedByValue]
        },
        [updatedOn]: fields[updatedOn]
    };
};

const getPatchDataSuccessAction = (page) => {
    let result = empty;

    if (page === ROLE_GROUP_LIST_PAGE) {
        result = patchTableDataSuccess;
    } else if (page === JOBS_PAGE_ALIAS || page === ROLE_INBOX_PAGE_ALIAS) {
        result = patchPagedDataSuccess;
    }

    return result;
};

const createPatchDataSuccessAction = (payload, tableData, currentPage) => {
    const { tableName, tableDataEntryGuid, moduleName } = payload;
    const currentPagedDatasAlias = getGridDatasAlias(currentPage);

    const metaData = {
        tableDataGuid: tableName,
        tableName: tableName,
        tableDataEntryGuid,
        tableData,
        moduleName
    };

    const patchDataSuccessAction = getPatchDataSuccessAction(currentPage);

    return patchDataSuccessAction(currentPagedDatasAlias, metaData, true);
};

const getPageGridUpdateSystemInfoActions = (payload, systemFields, tableName, page) => {
    const { updatedByGuid, updatedOn, updatedByValue } = getSystemFieldNames(tableName);
    let actions = [];
    const primaryFieldTableName = TABLE_NAMES.RESOURCE;
    const data = {
        value: systemFields[updatedByGuid].value,
        id: systemFields[updatedByGuid].id
    };

    const tableDatas = getEntityOptionFields(data, primaryFieldTableName);
    const currentTableDatasAlias = getTableDatasAlias(page);

    actions.push(of(loadMoreTableDataSuccess(
        currentTableDatasAlias,
        {
            tableDataGuid: primaryFieldTableName,
            tableNames: [primaryFieldTableName]
        },
        [tableDatas]
    )));

    const tableData = {
        [updatedByGuid]: systemFields[updatedByGuid].id,
        [updatedOn]: systemFields[updatedOn],
        [updatedByValue]: systemFields[updatedByGuid].value
    };

    actions.push(of(createPatchDataSuccessAction(payload, tableData, page)));

    return actions;
};

export const onDetailsPaneSuccessEdit$ = (alias) => {
    return (action$, state$, { apis }) => {
        return action$
            .ofType(`${actionTypes.ENTITY_WINDOW.DRIVEN_FIELD_ACTIONS.SET_SYSTEM_INFO}_${alias}`)
            .pipe(
                switchMap(({ payload }) => {
                    const { tableName, tableDataEntryGuid } = payload;
                    const { updatedByGuid, updatedByValue, updatedOn } = getSystemFieldNames(tableName);
                    const selection = {
                        fields: [
                            {
                                fieldName: updatedByGuid
                            },
                            {
                                fieldName: updatedOn
                            },
                            {
                                fieldName: updatedByValue
                            }
                        ],
                        filter: {
                            filterGroupOperator: 'And',
                            filterLines: [
                                {
                                    field: `${tableName}_guid`,
                                    operator: 'Contains',
                                    value: [tableDataEntryGuid]
                                }]
                        }
                    };

                    return apis['tableData'].getFilterTableAccessData$(tableName, selection);
                },
                (action, result) => [action, result]),
                mergeMap(
                    ([action, result]) => {
                        const getFieldInfo = getFieldInfoSelector(state$.value);
                        const { tableName } = action.payload;
                        const fields = buildSystemInfoFieldsStructure(result[0], getSystemFieldNames(tableName));
                        let chain = [];

                        Object.keys(fields).forEach(fieldName => {
                            chain.push(of(entityWindowFieldChanged(alias, getFieldInfo(tableName, fieldName), fields[fieldName])));
                        });

                        const currentPage = getCurrentPageAliasSelector(state$.value);
                        const pageGridTableName = state$.value[currentPage].tableName;

                        if ([ROLE_GROUP_LIST_PAGE, JOBS_PAGE_ALIAS, ROLE_INBOX_PAGE_ALIAS].includes(currentPage) && tableName === pageGridTableName) {

                            chain = [
                                ...chain,
                                ...getPageGridUpdateSystemInfoActions(action.payload, fields, tableName, currentPage)
                            ];
                        }

                        return 0 < chain.length ? concat(...chain) : empty();
                    }
                )
            );
    };
};

export const getBookingJobHoursOverbudget$ = (action$, state$, { apis }) => {
    const getLinkedDataWrappedSelector = createGetLinkedDataWrappedSelector();

    return action$
        .ofType(actionTypes.ENTITY_WINDOW.DRIVEN_FIELD_ACTIONS.GET_BOOKING_JOB_HOURS_OVERBUDGET)
        .pipe(
            switchMap((action) => {
                const state = state$.value;
                const isEnabledBudgetHoursAndRevenue = getFeatureFlagSelector(FEATURE_FLAGS.BUDGET_HOURS_AND_REVENUE)(state);

                if (!isEnabledBudgetHoursAndRevenue) {
                    return EMPTY;
                }

                const { moduleName, tableName, entity } = action.payload;

                if (!entity || !entity[BOOKING_JOB_GUID] || !entity[BOOKING_STATUS]) {
                    return EMPTY;
                }

                const { entityWindow } = state;

                const getLinkedData = getLinkedDataWrappedSelector({
                    tableStructure: getTableStructure(state),
                    primaryTableName: entityWindow.window[moduleName].tableName,
                    autoComplete: entityWindow.autoComplete[moduleName],
                    dataCollections: getPageTableDatasSelector(state)
                });

                if (entity && entity[BOOKING_STATUS]) {
                    const bookingStatus = getLinkedData(BOOKING_STATUS, entity[BOOKING_STATUS]);

                    if (bookingStatus && bookingStatus.value === BOOKING_TYPES.UNCONFIRMED) {
                        return of(clearFieldValueMessages({
                            tableName,
                            fieldName: BOOKING_TIME_ALLOCATION,
                            messageId: boookingJobHoursOverBudgetMessage.id
                        }));
                    }
                }

                const selection = {
                    fields: [
                        {
                            fieldName: JOB_BOOKINGS_CALC_FIELDS.HOURS_PERCENTAGE_BUDGET
                        }
                    ],
                    filter: {
                        filterGroupOperator: 'And',
                        filterLines: [
                            {
                                field: JOB_GUID,
                                operator: 'Contains',
                                value: [entity[BOOKING_JOB_GUID]]
                            }]
                    }
                };

                return apis['tableData'].getFilterTableAccessData$(TABLE_NAMES.JOB, selection)
                    .pipe(map(result => {
                        return { action, result };
                    }));
            }),
            mergeMap(({ action, result }) => {
                if (!action) {
                    return of(clearFieldValueMessages({
                        tableName: TABLE_NAMES.BOOKING,
                        fieldName: BOOKING_TIME_ALLOCATION,
                        messageId: boookingJobHoursOverBudgetMessage.id
                    }));
                }
                const { tableName } = action.payload;
                const state = state$.value;

                if (!result || result.length <= 0) {
                    return EMPTY;
                }

                const jobHoursPercentageBudget = result[0][JOB_BOOKINGS_CALC_FIELDS.HOURS_PERCENTAGE_BUDGET] || 0;

                if (jobHoursPercentageBudget < 100) {
                    return of(clearFieldValueMessages({
                        tableName,
                        fieldName: BOOKING_TIME_ALLOCATION,
                        messageId: boookingJobHoursOverBudgetMessage.id
                    }));
                }

                const translations = getTranslationsSelector(state, { sectionName: 'entityWindow' });
                const commonAliases = buildCommonEntityAliasesPlaceholdersSelector(state);
                const translationPlaceholders = { ...commonAliases, jobHoursPercentageBudget: jobHoursPercentageBudget.toFixed(2) };
                const translatedMessageText = populateStringTemplates(translations.messages, translationPlaceholders).bookingJobHoursOverBudgetMessageText;

                return of(setFieldValueMessages({
                    tableName,
                    fieldName: BOOKING_TIME_ALLOCATION,
                    message: {
                        [boookingJobHoursOverBudgetMessage.id]: {
                            ...boookingJobHoursOverBudgetMessage,
                            text: translatedMessageText
                        }
                    } }));
            })
        );
};

export const drivenActionsEpics$ = combineEpics(
    jobDrivenActionEpics,
    handleClearBookingWorkNonworkDaysExplanation$,
    handleClearRolerequestGroupFieldValue$,
    handleBarMultipleResourceChanged$,
    handleUpdateBudgetSection$,
    handleSectionMessagesUpdate$,
    handleRolerequestEntityWindowsMessageUpdate$,
    createHandleUpdateFieldOnContextualEdit(JOB_BUDGET_CONSUMED, calculateBudgetConsumed),
    createHandleUpdateFieldOnContextualEdit(JOB_PROFITMARGIN, calculateProfitMargin),
    createHandleUpdateFieldOnContextualEdit(JOB_TOTALPROFIT, calculateTotalProfit),
    handleUpdateBudgetSectionVisibility$,
    handleUpdateResourcesGuids$,
    handleUpdateFieldTableData$,
    handleUpdatePageParamsData$,
    validateActiveEntity$,
    validateMultipleEntitiesEpic$,
    getResourceTinyAvatar$,
    handleUpdateResourcesGuidsRoleCreation$,
    onDetailsPaneSuccessEdit$(ENTITY_WINDOW_MODULES.PLANNER_PAGE_DETAILS_PANE),
    onDetailsPaneSuccessEdit$(ENTITY_WINDOW_MODULES.PLANNER_PAGE_BATCH_DETAILS_PANE),
    onDetailsPaneSuccessEdit$(ENTITY_WINDOW_MODULES.JOBS_PAGE_DETAILS_PANE),
    onDetailsPaneSuccessEdit$(ENTITY_WINDOW_MODULES.JOBS_PAGE_RESOURCE_DETAILS_PANE),
    onDetailsPaneSuccessEdit$(ENTITY_WINDOW_MODULES.JOBS_PAGE_ROLE_GROUP_DETAILS_PANE),
    onDetailsPaneSuccessEdit$(ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_DETAILS_PANE),
    onDetailsPaneSuccessEdit$(ENTITY_WINDOW_MODULES.ROLE_INBOX_PAGE_BATCH_DETAILS_PANE),
    combineAPIEpics(
        handleSetBookingWorkNonworkDaysExplanation$,
        handleTimeAllocationReadonlyExplanation$,
        setTimeAllocationFTEExplanation$,
        handleBarTimeAllocationValues$,
        handleBarDateRangeChange$,
        getResourceDiaries$,
        loadBookingMultipleResourcesAvatars$,
        handleBarResourceDescriptionReadonlyExplanation$,
        handleBarChargeRateValues$,
        handleLoadResourceChargeRateCurrentValue$,
        getResourcesCount$,
        handleGetEntityAuditData$,
        handleRejectReasonsExplanation$,
        setBookingStatusExplanation$
    ),
    roleGroupDrivenActionsEpics$,
    getBookingJobHoursOverbudget$
);