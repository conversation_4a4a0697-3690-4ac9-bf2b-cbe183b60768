import { dataGridLoadData, dataGridPageChange } from '../../actions/dataGridActions';
import { setPageStateDirty, updatePageParams } from '../../actions/pageStateActions';
import { reloadPlannerData } from '../../actions/plannerDataActions';
import { loadTPData } from '../../actions/talentProfileActions';
import { filtersModelsLoadedSuccess, loadWorkspaces, populateSkillsValues } from '../../actions/workspaceActions';
import { loadListPageWorkspaces } from '../../actions/listPageActions';
import { PLANNER_PAGE_ALIAS, TABLE_NAMES } from '../../constants';
import { DATA_GRID_INITIAL_PAGE_NUMBER } from '../../constants/dataGridConsts';
import { JOBSPAGE_FILTER_ALIAS, JOBS_PAGE_ALIAS } from '../../constants/jobsPageConsts';
import { isEmptyObject } from '../../utils/commonUtils';
import { isEqual } from 'lodash';
import { URL_PARAMS } from '../../constants/globalConsts';
import { addDetailsPaneModel } from '../../actions/detailsPaneActions';
import { JOBS_PAGE_DP_ALIAS } from '../../constants/jobsPageConsts';
import { navBarPageChange, PAGE_ACTIONS, replaceUrl } from '../../actions/navigateActions';
import { getPagedData, getPageState } from '../../selectors/pagesSelectors';
import { ROLE_INBOX_PAGE_ALIAS, ROLE_INBOX_PAGE_FILTER_ALIAS, ROLE_INBOX_PAGE_DP_ALIAS } from '../../constants/roleInboxPageConsts';
import { notificationsLoadData } from '../../actions/notificationsPageActions';
import { digestRoleInboxDataGridLoadData } from '../../actions/roleInboxPageActions';
import { onDateSelection } from '../../actions/timesheetsActions';
import { setDataGridColumns } from '../../actions/jobsActions';
import { refreshPageRoleAssignees } from '../../actions/rolerequestActions';
import { getEntityStructure } from '../../selectors/entityStructureSelectors';
import { getIsCriteriaRole } from '../../utils/roleRequestsUtils';
import { ROLEREQUEST_FIELDS } from '../../constants/fieldConsts';
import { MARKETPLACE_PAGE_ALIAS, MARKETPLACE_PAGE_DP_ALIAS } from '../../constants/marketplacePageConsts';
import { TABLE_VIEW_PAGE_ALIAS } from '../../constants/tableViewPageConsts';
import { EXECUTE_INITIAL_LOAD_PLANNER_PAGE } from '../../actions/actionTypes';
import { restoreUserLongRunningTask } from '../../reducers/longRunningTasksReducer';
import { LONG_RUNNING_TASK_TYPES } from '../../reducers/longRunningTasksReducer/state';
import { fetchSkillEntityTypes } from '../../actions/adminSettings/adminSettingActions';
import { loadSkillPreferences } from '../../actions/resourceSkillsActions';
import { RESOURCES_PAGE_ALIAS, RESOURCES_PAGE_DP_ALIAS, RESOURCES_PAGE_FILTER_ALIAS } from '../../constants/resourcesPageConsts';

export const LOAD_PAGE_DATA_STRATEGIES = {
    INITIAL_LOAD: 'INITIAL_LOAD',
    LOAD_FROM_DIRTY: 'LOAD_FROM_DIRTY',
    LOAD_FROM_PARAMS: 'LOAD_FROM_PARAMS',
    CONSEQUENT_LOAD: 'CONSEQUENT_LOAD'
};

const {
    WORKSPACE_SURROGATE_ID,
    PAGE,
    PAGE_SIZE,
    RESOURCE_SURROGATE_ID,
    JOB_SURROGATE_ID,
    ROLE_GROUP_SURROGATE_ID
} = URL_PARAMS;


const getPlannerPageLoadStrategy = (previousPageState, currentPageState = {}) => {
    let strategy;

    if (isEmptyObject(previousPageState)) {
        strategy = LOAD_PAGE_DATA_STRATEGIES.INITIAL_LOAD;
    } else if (previousPageState.dirty === true) {
        strategy = LOAD_PAGE_DATA_STRATEGIES.LOAD_FROM_DIRTY;
    } else if (previousPageState.params.workspaceName && !(currentPageState.params || {}).workspaceName) {
        strategy = LOAD_PAGE_DATA_STRATEGIES.CONSEQUENT_LOAD;
    } else {
        strategy = LOAD_PAGE_DATA_STRATEGIES.LOAD_FROM_PARAMS;
    }

    return strategy;
};

const getPlannerPageStrategyImplementingActions = (strategy, pageParams, prevStateParams = {}) => {
    let actions = [];
    const groupWorkspacesByType = false;
    const getMostRecentlyUsedWorkspacesDetails = true;
    const selectedWorkspaceSurrogateId = pageParams[WORKSPACE_SURROGATE_ID];

    switch (strategy) {
        case LOAD_PAGE_DATA_STRATEGIES.INITIAL_LOAD: {
            actions.push(
                {
                    type: EXECUTE_INITIAL_LOAD_PLANNER_PAGE
                },
                restoreUserLongRunningTask(LONG_RUNNING_TASK_TYPES.DUPLICATE_JOB)
            );
            break;
        }
        case LOAD_PAGE_DATA_STRATEGIES.LOAD_FROM_DIRTY: {
            actions.push(
                reloadPlannerData(),
                populateSkillsValues(PLANNER_PAGE_ALIAS),
                setPageStateDirty(PLANNER_PAGE_ALIAS, false)
            );
            break;
        }
        case LOAD_PAGE_DATA_STRATEGIES.LOAD_FROM_PARAMS: {
            actions.push(
                loadWorkspaces(groupWorkspacesByType, getMostRecentlyUsedWorkspacesDetails, selectedWorkspaceSurrogateId),
                restoreUserLongRunningTask(LONG_RUNNING_TASK_TYPES.DUPLICATE_JOB)
            );
            break;
        }
        case LOAD_PAGE_DATA_STRATEGIES.CONSEQUENT_LOAD: {
            actions.push(
                updatePageParams(PLANNER_PAGE_ALIAS, prevStateParams),
                replaceUrl(prevStateParams),
                restoreUserLongRunningTask(LONG_RUNNING_TASK_TYPES.DUPLICATE_JOB)
            );
            break;
        }
        default:
            break;
    }

    return actions;
};

const getJobsPageLoadStrategy = (pageParams, stateParams) => {
    const urlPageParam = pageParams[PAGE];
    const urlPageSizeParam = pageParams[PAGE_SIZE];
    const { pagedData } = stateParams;
    const { loadedPages, pageSize: statePageSize } = pagedData;
    const pageLoaded = loadedPages.indexOf(urlPageParam - 1) > -1;

    let strategy;

    if (pageLoaded && statePageSize == urlPageSizeParam) {
        strategy = LOAD_PAGE_DATA_STRATEGIES.LOAD_FROM_PARAMS;
    } else {
        strategy = LOAD_PAGE_DATA_STRATEGIES.INITIAL_LOAD;
    }

    return strategy;
};

const getJobsPageStrategyImplementingActions = (strategy, pageParams = {}, entityStructure, displayFields) => {
    const urlPageParam = pageParams[PAGE];
    const urlPageSizeParam = pageParams[PAGE_SIZE];
    let actions = [];

    switch (strategy) {
        case LOAD_PAGE_DATA_STRATEGIES.INITIAL_LOAD: {
            actions.push(
                setDataGridColumns(JOBS_PAGE_ALIAS, displayFields),
                dataGridLoadData(JOBS_PAGE_ALIAS, urlPageSizeParam, DATA_GRID_INITIAL_PAGE_NUMBER, urlPageParam),
                filtersModelsLoadedSuccess(JOBSPAGE_FILTER_ALIAS),
                addDetailsPaneModel(JOBS_PAGE_DP_ALIAS, entityStructure, JOBS_PAGE_DP_ALIAS),
                restoreUserLongRunningTask(LONG_RUNNING_TASK_TYPES.DUPLICATE_JOB),
                loadListPageWorkspaces(false)
            );
            break;
        }
        case LOAD_PAGE_DATA_STRATEGIES.LOAD_FROM_PARAMS: {
            actions.push(
                dataGridPageChange(JOBS_PAGE_ALIAS, urlPageParam, urlPageSizeParam),
                restoreUserLongRunningTask(LONG_RUNNING_TASK_TYPES.DUPLICATE_JOB)
            );
            break;
        }
        default:
            break;
    }

    return actions;
};

const getResourcesPageLoadStrategy = (pageParams, stateParams) => {
    const urlPageParam = pageParams[PAGE];
    const urlPageSizeParam = pageParams[PAGE_SIZE];
    const { pagedData } = stateParams;
    const { loadedPages, pageSize: statePageSize } = pagedData;
    const pageLoaded = loadedPages.indexOf(urlPageParam - 1) > -1;

    let strategy;

    if (pageLoaded && statePageSize == urlPageSizeParam) {
        strategy = LOAD_PAGE_DATA_STRATEGIES.LOAD_FROM_PARAMS;
    } else {
        strategy = LOAD_PAGE_DATA_STRATEGIES.INITIAL_LOAD;
    }

    return strategy;
};

const getResourcesPageStrategyImplementingActions = (strategy, pageParams = {}, entityStructure, displayFields) => {
    const urlPageParam = pageParams[PAGE];
    const urlPageSizeParam = pageParams[PAGE_SIZE];
    let actions = [];

    switch (strategy) {
        case LOAD_PAGE_DATA_STRATEGIES.INITIAL_LOAD: {
            actions.push(
                setDataGridColumns(RESOURCES_PAGE_ALIAS, displayFields),
                dataGridLoadData(RESOURCES_PAGE_ALIAS, urlPageSizeParam, DATA_GRID_INITIAL_PAGE_NUMBER, urlPageParam),
                filtersModelsLoadedSuccess(RESOURCES_PAGE_FILTER_ALIAS),
                addDetailsPaneModel(RESOURCES_PAGE_DP_ALIAS, entityStructure, RESOURCES_PAGE_DP_ALIAS)
            );
            break;
        }
        case LOAD_PAGE_DATA_STRATEGIES.LOAD_FROM_PARAMS: {
            actions.push(
                dataGridPageChange(JOBS_PAGE_ALIAS, urlPageParam, urlPageSizeParam),
                restoreUserLongRunningTask(LONG_RUNNING_TASK_TYPES.DUPLICATE_JOB)
            );
            break;
        }
        default:
            break;
    }

    return actions;
};

const getProfilePageLoadStrategy = () => {
    return LOAD_PAGE_DATA_STRATEGIES.INITIAL_LOAD;
};

const getProfilePageStrategyImplementingActions = (strategy) => {
    let actions = [];

    switch (strategy) {
        case LOAD_PAGE_DATA_STRATEGIES.INITIAL_LOAD: {
            actions.push(loadTPData());
            actions.push(fetchSkillEntityTypes());
            actions.push(loadSkillPreferences());
            break;
        }
        default:
            break;
    }

    return actions;
};

const getRoleInboxLoadStrategy = (pageParams, stateParams) => {
    const urlPageParam = pageParams[PAGE];
    const urlPageSizeParam = pageParams[PAGE_SIZE];
    const { pagedData } = stateParams;
    const { loadedPages, pageSize: statePageSize } = pagedData;
    const pageLoaded = loadedPages.indexOf(urlPageParam - 1) > -1;

    let strategy;

    if (pageLoaded && statePageSize == urlPageSizeParam) {
        strategy = LOAD_PAGE_DATA_STRATEGIES.LOAD_FROM_PARAMS;
    } else {
        strategy = LOAD_PAGE_DATA_STRATEGIES.INITIAL_LOAD;
    }

    return strategy;
};

const getRoleInboxPageStrategyImplementingActions = (strategy, state) => {
    const { params = {} } = getPageState(state, ROLE_INBOX_PAGE_ALIAS);
    const urlPageParam = params[PAGE];
    const urlPageSizeParam = params[PAGE_SIZE];
    const entityStructure = getEntityStructure(state);
    let actions = [];

    switch (strategy) {
        case LOAD_PAGE_DATA_STRATEGIES.INITIAL_LOAD: {
            actions.push(
                filtersModelsLoadedSuccess(ROLE_INBOX_PAGE_FILTER_ALIAS),
                addDetailsPaneModel(ROLE_INBOX_PAGE_DP_ALIAS, entityStructure, ROLE_INBOX_PAGE_DP_ALIAS),
                digestRoleInboxDataGridLoadData(ROLE_INBOX_PAGE_ALIAS, urlPageSizeParam, DATA_GRID_INITIAL_PAGE_NUMBER, urlPageParam)
            );
            break;
        }
        case LOAD_PAGE_DATA_STRATEGIES.LOAD_FROM_PARAMS: {
            actions.push(
                dataGridPageChange(ROLE_INBOX_PAGE_ALIAS, urlPageParam, urlPageSizeParam)
            );
            break;
        }
        case LOAD_PAGE_DATA_STRATEGIES.CONSEQUENT_LOAD: {
            const { data } = getPagedData(state, ROLE_INBOX_PAGE_ALIAS)[TABLE_NAMES.ROLEREQUEST];
            const criteriaRoleIds = (data || []).reduce((accumulator, role) => {
                getIsCriteriaRole(role) && accumulator.push(role[ROLEREQUEST_FIELDS.GUID]);

                return accumulator;
            }, []);

            actions.push(refreshPageRoleAssignees(criteriaRoleIds));
            break;
        }
        default:
            break;
    }

    return actions;
};

const getRoleInboxPageConsequentLoadStrategyActions = () => {
    return [PAGE_ACTIONS.LOAD[ROLE_INBOX_PAGE_ALIAS](LOAD_PAGE_DATA_STRATEGIES.CONSEQUENT_LOAD)];
};

const getTimesheetsLoadStrategy = () => {
    return LOAD_PAGE_DATA_STRATEGIES.INITIAL_LOAD;
};

const getTimesheetsPageStrategyImplementingActions = (strategy, selectedDateRange = {}, selectedDate = undefined) => {
    let actions = [];

    switch (strategy) {
        case LOAD_PAGE_DATA_STRATEGIES.INITIAL_LOAD: {
            actions.push(
                onDateSelection({ selectedDateRange, selectedDate })
            );
            break;
        }
        default:
            break;
    }

    return actions;
};

const getNotificationsLoadStrategy = () => {
    return LOAD_PAGE_DATA_STRATEGIES.INITIAL_LOAD;
};

const getNotificationsPageStrategyImplementingActions = (strategy, tabParams = {}) => {
    let actions = [];

    switch (strategy) {
        case LOAD_PAGE_DATA_STRATEGIES.INITIAL_LOAD: {
            actions.push(
                notificationsLoadData(tabParams)
            );
            break;
        }
        default:
            break;
    }

    return actions;
};

const getMarketplaceLoadStategy = () => {
    return LOAD_PAGE_DATA_STRATEGIES.INITIAL_LOAD;
};

const getMarketplacePageStategyImplementatingActions = (strategy, tabParams = {}, entityStructure) => {
    let actions = [];
    const urlPageParam = tabParams[PAGE];
    const urlPageSizeParam = tabParams[PAGE_SIZE];

    switch (strategy) {
        case LOAD_PAGE_DATA_STRATEGIES.INITIAL_LOAD: {
            actions.push(
                addDetailsPaneModel(MARKETPLACE_PAGE_DP_ALIAS, entityStructure, MARKETPLACE_PAGE_DP_ALIAS),
                dataGridLoadData(MARKETPLACE_PAGE_ALIAS, urlPageSizeParam, DATA_GRID_INITIAL_PAGE_NUMBER, urlPageParam)
            );
            break;
        }
        case LOAD_PAGE_DATA_STRATEGIES.LOAD_FROM_PARAMS: {
            // const { alias, pageNumber, pageSize, targetPage } = action.payload;
            actions.push(
                dataGridLoadData(MARKETPLACE_PAGE_ALIAS, urlPageSizeParam, 1, urlPageParam)

                //Uncomment if we need to load assignees as well
                // dataGridPageChange(MARKETPLACE_PAGE_ALIAS, urlPageParam, urlPageSizeParam)
            );
            break;
        }
        default:
            break;
    }

    return actions;
};

const getTableViewLoadStategy = () => {
    return LOAD_PAGE_DATA_STRATEGIES.INITIAL_LOAD;
};

const getTableViewPageStategyImplementatingActions = (strategy, tabParams = {}, state) => {
    let actions = [navBarPageChange(TABLE_VIEW_PAGE_ALIAS, '')];

    switch (strategy) {
        case LOAD_PAGE_DATA_STRATEGIES.INITIAL_LOAD:
        /*The 2 actions below are currently not used.
        There are epics created for them - adjust those epics on implementation
        case LOAD_PAGE_DATA_STRATEGIES.LOAD_FROM_DIRTY:
        case LOAD_PAGE_DATA_STRATEGIES.LOAD_FROM_PARAMS:*/ {

            actions.push(
                { type: `${strategy}_${TABLE_VIEW_PAGE_ALIAS}` }
            );
            break;
        }
        default:
            break;
    }

    return actions;
};

const commonPageStateChanged = (currentState, previousState) => {
    return !isEqual(currentState, previousState);
};

const plannerPageStateChanged = (currentState, previousState) => {
    const {
        params: currentParams = {}
    } = currentState;
    const {
        params: previousParams = {},
        dirty: previousStateIsDirty
    } = previousState;

    return currentParams[WORKSPACE_SURROGATE_ID] != previousParams[WORKSPACE_SURROGATE_ID] || previousStateIsDirty;
};

const profilePageStateChanged = (currentState, previousState) => {
    const {
        params: currentParams = {}
    } = currentState;
    const {
        params: previousParams = {}
    } = previousState;

    return currentParams[RESOURCE_SURROGATE_ID] != previousParams[RESOURCE_SURROGATE_ID];
};

const roleGroupListPageStateChanged = (currentState, previousState) => {
    const {
        params: currentParams = {}
    } = currentState;
    const {
        params: previousParams = {}
    } = previousState;

    return currentParams[JOB_SURROGATE_ID] != previousParams[JOB_SURROGATE_ID];
};

const roleGroupDetailsPageStateChanged = (currentState, previousState) => {
    const {
        params: currentParams = {}
    } = currentState;
    const {
        params: previousParams = {}
    } = previousState;

    return currentParams[ROLE_GROUP_SURROGATE_ID] != previousParams[ROLE_GROUP_SURROGATE_ID];
};

export {
    getPlannerPageLoadStrategy,
    getPlannerPageStrategyImplementingActions,
    getJobsPageLoadStrategy,
    getJobsPageStrategyImplementingActions,
    getResourcesPageLoadStrategy,
    getResourcesPageStrategyImplementingActions,
    getProfilePageLoadStrategy,
    getProfilePageStrategyImplementingActions,
    getRoleInboxLoadStrategy,
    getRoleInboxPageConsequentLoadStrategyActions,
    getRoleInboxPageStrategyImplementingActions,
    commonPageStateChanged,
    plannerPageStateChanged,
    profilePageStateChanged,
    roleGroupListPageStateChanged,
    roleGroupDetailsPageStateChanged,
    getTimesheetsLoadStrategy,
    getTimesheetsPageStrategyImplementingActions,
    getNotificationsLoadStrategy,
    getNotificationsPageStrategyImplementingActions,
    getMarketplaceLoadStategy,
    getMarketplacePageStategyImplementatingActions,
    getTableViewLoadStategy,
    getTableViewPageStategyImplementatingActions
};