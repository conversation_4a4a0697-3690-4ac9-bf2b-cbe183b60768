import { combineEpics } from "redux-observable";
import { EMPTY, of, forkJoin } from "rxjs";
import { format } from "date-fns";
import uuidv4 from 'uuid/v4';
import * as wsActions from '../actions/workspaceActions';
import * as actionTypes from '../actions/actionTypes';
import { getApplicationUserId } from "../selectors/applicationUserSelectors";
import { createAPICallEpic, getApiCallEpicConfig } from "./epicGenerators/apiCallEpicGenerator";
import { generateDefaultNewPlanName } from "../utils/planNameGenerationUtils";
import { combineAPIEpics } from "./middlewareExtensions";
import { map, switchMap } from "rxjs/operators";
import { getCurrentDate } from "../utils/dateUtils";
import { getActiveListViewSelector, getPageFiltersSelector, getWorkspaceViewTypeSelector } from "../selectors/listPageSelectors";
import { J<PERSON><PERSON>_PAGE_ALIAS, TABLE_NAMES } from "../constants";
import { RESOURCES_PAGE_ALIAS } from "../constants/resourcesPageConsts";
import { WORKSPACE_VIEW_TYPE_FIELDS } from "../constants/fieldConsts";
import { digestCreateListPageWorkspaceSuccess, updateWorkspaceViewType } from "../actions/listPageActions";

const workspacesApiName = 'workspaces';
const tableDataApiName = 'tableData';

export const ensureWorkspaceNameSet$ = (api, payload) => {
    const { workspace_description, privatePlans, newPlanLabel } = payload;

    const workspaceName = !workspace_description || workspace_description === ''
        ? generateDefaultNewPlanName(privatePlans, newPlanLabel)
        : workspace_description;

    return of(workspaceName);
};

const handleLoadWorkspacesEpic = (action$, state$, { apis }) => {
    return action$.ofType(
        actionTypes.LIST_PAGE_ACTIONS.LOAD_WORKSPACES
    ).pipe(
        switchMap((action) => {
            const { payload } = action;
            const selection = {
                fields: [
                    { fieldName: WORKSPACE_VIEW_TYPE_FIELDS.GUID },
                    { fieldName: WORKSPACE_VIEW_TYPE_FIELDS.DESCRIPTION }
                ],
                filter: {
                    filterGroupOperator: 'And',
                    filterLines: []
                }
            };

            return apis[tableDataApiName].getFilterTableAccessData$(TABLE_NAMES.WORKSPACE_VIEW_TYPE, selection)
                .pipe(map(result => {
                    return { result, payload };
                }));
        }),
        switchMap(({ result, payload }) => {
            if (!result || result.length <= 0) {
                return EMPTY;
            }

            let wsViewType = result.find(viewType => viewType[WORKSPACE_VIEW_TYPE_FIELDS.DESCRIPTION] === 'Lists');

            if (!wsViewType) {
                // Show error creating workspace
                return EMPTY;
            }

            // Use the same approach as planner page - call both APIs using forkJoin
            return forkJoin(
                apis[workspacesApiName].getWorkspaces$(payload.groupByType, wsViewType[WORKSPACE_VIEW_TYPE_FIELDS.GUID]),
                apis[workspacesApiName].getMostRecentlyUsed$(payload.getWorkspaceDetail || false)
            ).pipe(map(result => {
                return { result, wsViewType };
            }));
        }),
        switchMap(({ result, wsViewType }) => {
            console.log('workspaces result:', result);
            // Dispatch both the workspace view type update and the workspace data loading success
            return of(
                updateWorkspaceViewType(wsViewType),
                {
                    type: actionTypes.LIST_PAGE_ACTIONS.LOAD_WORKSPACES_SUCCESSFUL,
                    payload: {
                        data: result
                    }
                }
            );
        })
    )
}

const handleDigestCreateWorkspaceSuccessEpic = (action$, state$, { apis }) => {
    return action$.ofType(
        actionTypes.LIST_PAGE_ACTIONS.CREATE_WORKSPACE_SUCCESSFUL
    ).pipe(
        switchMap(({ payload }) => {
            const state = state$.value;
            const wsViewType = getWorkspaceViewTypeSelector(state);
            
            if (!wsViewType) {
                // Show error creating workspace
                return EMPTY;
            }
                       
            const { data, wsDescription, wsAccessType, wsEditRights, newWorkspaceTemplateGuid, doCreate, selectCreatedWorkspace } = payload;            
            const newWorkspaceDescription = data ? data : wsDescription;
            const activeListView = getActiveListViewSelector(state);
            const currentUserGuid = getApplicationUserId(state);
            const getPageFilters = getPageFiltersSelector(state);

            const newWorkspaceStructure = {
                workspace_description: newWorkspaceDescription,
                workspace_accesstype: wsAccessType,
                workspace_editrights: wsEditRights,
                workspace_colourtheme_guid: null,
                workspace_colour_field_name: null,
                workspace_change_date: format(getCurrentDate(), 'YYYY-MM-DDTHH:mm:ss.SSS'),
                workspace_author_guid: currentUserGuid,
                workspace_change_author_guid: currentUserGuid,
                workspace_custom_colour_field: [],
                workspace_wsviewtype_guid: wsViewType[WORKSPACE_VIEW_TYPE_FIELDS.GUID]
            };

            const workSpaceSettings = {
                activeListView,
                views: {
                    [JOBS_PAGE_ALIAS]: {
                        filters: getPageFilters(JOBS_PAGE_ALIAS, TABLE_NAMES.JOB)
                    },
                    [RESOURCES_PAGE_ALIAS]: {
                        filters: getPageFilters(RESOURCES_PAGE_ALIAS, TABLE_NAMES.RESOURCE)
                    },
                }
            };

            newWorkspaceStructure['workspace_settings'] = JSON.stringify(workSpaceSettings);

            console.log('handleDigestCreateWorkspaceSuccessEpic:', {newWorkspaceStructure, workSpaceSettings});
            
            return apis[workspacesApiName].insertWorkspace$(newWorkspaceStructure);
        }),
        switchMap((result) => {
            console.log('workspace created:', result);
            return EMPTY;
        })
    );
};

export const digestCreateWorkspaceEpic = (successActionHandler = digestCreateListPageWorkspaceSuccess) =>
    createAPICallEpic(
        null,
        getApiCallEpicConfig(actionTypes.LIST_PAGE_ACTIONS.CREATE_WORKSPACE, workspacesApiName, ensureWorkspaceNameSet$, successActionHandler, null)
    );

export default combineEpics(
    combineAPIEpics(
        digestCreateWorkspaceEpic(digestCreateListPageWorkspaceSuccess)(),
    ),
    handleLoadWorkspacesEpic,
    handleDigestCreateWorkspaceSuccessEpic
);