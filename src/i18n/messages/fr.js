﻿// Any string wrapped in ${ } or { } is a placeholder string and should not be translated literally.
export default {
    //common validation errors
    emailValidationError: 'Veuillez indiquer une adresse e-mail valide.',
    //commonly used text
    fieldValue: 'Valeur de champ',
    confirmation: 'Confirmation',
    invalid: 'Non valide',
    current: '{index} (Actuel)',
    dismissText: 'Ignorer',
    mandatoryValidation: '* Obligatoire',
    //common component
    selectionListLicenseWarning: '{contactUs} pour augmenter votre limite.',
    selectionListLicenseError: '{contactUs} pour augmenter votre limite.',
    selectionListDiaryCustomMsg: '{activeCount} journaux utilisés sur {licensedCount}',
    selectionListSkillsCustomMsg: '{activeCount} de {licensedCount} compétences utilisées',
    commonComponentConfirmationModalConsequence: 'Je comprends les conséquences de ce changement.',
    commonComponentConfirmationModalEmptyMsg: ' ',
    noOptionsAvailable: 'Aucune option disponible',
    actionBarSaveButtonLabel: 'Enregistrer les modifications',
    actionBarConfirmChangesLabel: 'Confirmer les modifications',
    actionBarCancelButtonLabel: 'Annuler',
    actionBarDiscardChangesLabel: 'Annuler les modifications',
    actionBarLeavePageMessage: 'Voulez-vous vraiment quitter cette page ?',
    actionBarUnsavedChanges: 'La page présente des modifications non enregistrées. Voulez-vous tout de même la quitter ?',
    actionBarFormErrorMessage: 'Ce formulaire contient des erreurs.',
    deleteModalCancelButtonLabel: 'Non, conserver le profil de sécurité',
    deleteModalSubmitButtonLabel: 'Oui, supprimer le profil de sécurité',
    securityProfileDeleteModalTitle: 'Supprimer le profil de sécurité {profileName} ?',
    securityProfileDeleteModalText: 'Voulez-vous supprimer définitivement ce profil de sécurité ?',
    cannotBeUndoneText: 'Cette action ne peut être annulée.',
    addAnItemText: 'Ajouter un élément',
    selectionListSortText: 'Trier de A à Z',
    deleteSectionConfirmation: 'Voulez-vous vraiment supprimer {sectionName} ?',
    deleteSectionConfirmationTitle: 'Confirmation de suppression de section',
    CreateContextMenuLabel: 'Créer',
    RenameContextMenuLabel: 'Renommer',
    DuplicateContextMenuLabel: 'Dupliquer',
    DeleteContextMenuLabel: 'Supprimer',
    DUPLICATE_USING_APIContextMenuLabel: 'DUPLICATE_USING_API',
    toasterDefaultSuccessMessage: 'Modifications enregistrées',
    toasterDefaultErrorMessage: 'L\'enregistrement a échoué, veuillez réessayer',
    toasterDefaultWarningMessage: 'Certaines modifications n’ont pas été enregistrées, veuillez actualiser et réessayer',
    toasterDefaultUnsavedChangesMessage: 'Impossible d’enregistrer les modifications, veuillez corriger les erreurs et réessayer.',
    actionCannotBeUndoneMessage: 'Cette action ne peut pas être annulée.',
    noDataAvailableText: 'Pas de données',
    licensedCountLabel: '{activeCount} utilisés sur {licensedCount}',
    defaultLimitationInfo: 'La limitation est basée sur votre plan de licence actuel.',
    contactUsText: 'Contactez-nous',
    importText: 'Importer',
    noResultsFound: 'Aucun résultat trouvé',
    reorderText: 'Réordonner',
    editText: 'Modifier',
    resetText: 'Réinitialiser',
    searchText: 'Chercher',
    nameText: 'nom',

    //skill filter cascader component
    skillFilterResetButtonDisabledMessage: 'Réinitialisation impossible : aucune compétence sélectionnée.',
    skillFilterApplyButtonDisabledMessage: 'Application impossible : aucune compétence sélectionnée.',
    skillFilterApplyButtonDisabledForMaxCountMessage: 'Application impossible : maximum {maxSkillSelection} sélections autorisées.',

    //Import data
    entityImportSelectValuePlaceholder: 'Sélectionnez une valeur',
    ImportSuccessfulToasterMessage: 'Importation réussie',
    ImportUnsuccessfulToasterMessage: 'L’importation a échoué',
    operationLogChangesSaved: 'Modifications apportées au journal d\'opérations enregistrées',
    //userManagement Messages
    userManagementLicenseWarning: 'Vous êtes proche de la limite autorisée par votre licence. Passez les utilisateurs existants sur \'Inactif\' ou {contactUs} pour augmenter la limite de votre plan.',
    userManagementLicenseError: 'Vous avez atteint la limite autorisée par votre licence. Passez les utilisateurs existants sur \'Inactif\' ou {contactUs} pour augmenter la limite de votre plan.',
    userManagementLicenseErrorHeading: 'Impossible d’avoir plus de {number} utilisateurs actifs.',
    userManagementLicenseActiveUsers: 'Vous avez {number} utilisateurs définis sur « Actif ».',
    userManagementLicenseUserLimit: 'Votre licence actuelle prend en charge jusqu\'à {number} utilisateurs actifs.',
    userManagementLicenseContactUS: 'Passez certains utilisateurs sur \'Inactif\' ou {contactUs} pour augmenter votre limite.',
    userManagementInactiveUserHeading: 'Passer {number} utilisateurs sur \'Inactif\' ?',
    userManagementSetInactiveUserPopup: 'Souhaitez-vous passer {number} utilisateurs sur \'Inactif\' ?',
    userManagementUnAssignBooks: 'Ils ne pourront pas se connecter et seront désaffectés des réservations en cours. Il ne sera pas possible de les affecter à de futures réservations.',
    userManagementReportingArchive: 'S’ils étaient affectés à des réservations passées, ces données seront conservées à des fins de rapport et d’archivage.',
    userManagementInactiveConfirmation: 'Passez {number} utilisateurs sur \'Inactif\' et supprimez-les des réservations en cours',
    userManagementInactivePopupPrimaryButton: 'Rendre les utilisateurs inactifs',
    userManagementInactivePopupSecondaryButton: 'Annuler',
    //for confirmation modal popup need to change later
    userManagementDeletePopupMessage: 'Supprimer les utilisateurs sélectionnés ?',
    userManagementDeleteWarningMessage: 'Souhaitez-vous supprimer définitivement {number} utilisateurs ?',
    userManagementDeleteBookingsWarning: 'Toutes leurs informations seront supprimées du système. Les réservations passées auxquelles ils étaient affectés deviendront non attribuées, ce qui peut affecter les rapports impliquant ces réservations.',
    userManagementDeleteHistoricData: 'Si vous souhaitez conserver les données historiques, passez simplement l\'état de cet utilisateur sur \'Inactif\'.',
    userManagementDeleteConfirmation: 'Supprimer les utilisateurs sélectionnés et les supprimer de toutes les réservations et rapports dans le système',
    userManagementDeletePrimaryButton: 'Supprimer les utilisateurs',
    userManagementDeleteSecondaryButton: 'Conserver les utilisateurs sélectionnés',
    userManagementDescription: 'Ici, vous pouvez ajouter, modifier ou supprimer des utilisateurs. Accédez à leurs profils pour apporter d\'autres modifications.',
    userManagementSubTitle: 'Utilisateurs actifs',
    userManagementNotifyUserCountDescription: '{activeUser} utilisateurs sont actifs sur {totalUser}. Votre licence prend en charge jusqu\'à {licencedActiveUser} utilisateurs actifs.',
    userManagementAddUserButtonFroCommandBar: 'Ajouter un utilisateur',
    userManagementToNavigateImportFeatureMessage: 'Pour les mises à jour groupées, la fonction {Import} vous semblera sans doute plus pratique.',
    userManagementFormLabelsSecurityProfile: 'Profil de sécurité',
    userManagementFormLabelsUserStatusDescription: 'Définir d’un utilisateur sur actif lui permet de se connecter à son compte.',
    userManagementFormLabelsName: 'Nom',
    userManagementFormLabelsEmail: 'E-mail',
    userManagementFormLabelsUserStatus: 'Utilisateur actif',
    userManagementFormLabelsUserLastLogin: 'Dernière connexion',
    userManagementValidationFirstName: 'Saisissez le prénom',
    userManagementValidationLastName: 'Saisissez le nom de famille',
    userManagementValidationEmailReqd: 'Saisissez votre e-mail',
    userManagementValidationEmailMsg: 'Ce n\'est pas une adresse e-mail valide',
    userManagementValidationSecurityProfile: 'La sélection du profil de sécurité est obligatoire',
    userManagementTooltipGoToProfile: 'Aller au profil',
    userManagementTooltipResetPass: 'Réinitialiser le mot de passe',
    userManagementTooltipResendEmail: 'Envoyer un e-mail d’invitation',
    userManagementTooltipMarkDelete: 'Marquer pour suppression',
    userManagementFirstNamePlaceholder: 'Prénom',
    userManagementLastNamePlaceholder: 'Nom',
    userManagementEmailPlaceholder: 'Saisissez l’e-mail',
    userManagementSecurityProfilePlaceholder: 'Sélectionnez un profil de sécurité',
    userManagementTitle: 'Gestion des utilisateurs',
    userManagementStatusActive: 'Actif',
    userManagementStatusInactive: 'Inactif',
    userManagementDuplicateEmail: 'Veillez à ce que que les utilisateurs aient des identifiants de messagerie uniques.',
    userManagementUserAccess: 'Autorisations insuffisantes pour modifier ces ressources',
    //comopanyInfo Messages
    companyInfoNameLabel: 'Nom de la société',
    companyInfoLogoLabel: 'Logo',
    companyUploadLogoLabel: 'Logo d’importation',
    companyInfoLogoThumbnailLabel: 'Vignette du logo',
    companyUploadLogoThumbnailLabel: 'Vignette du logo d’importation',
    companyInfoSupportPhoneNumberLabel: 'Numéro de téléphone de l’assistance',
    companyInfoApplicationLanguage: 'Langue de l’application',
    companyInfoSupportEmailLabel: 'Adresse e-mail de l\'assistance',
    companyInfoInstanceOwnerLabel: 'Propriétaire de l\'instance',
    companyInfoLogoControlMessage: 'Le logo doit mesurer 80 x 24 px et être au format .png',
    companyInfoUploadControlText: 'Cliquez sur un fichier ou faites-le glisser dans cette zone pour l’importer',
    companyInfoLogoThumbnailControlMessage: 'La vignette du logo doit mesurer 24 x 24 px et être au format .png',
    companyInfoPhoneNumberErrorMessage: 'Veuillez indiquer un numéro de téléphone valide',
    companyInfoEmailErrorMessage: 'Veuillez indiquer une adresse e-mail valide',
    companyInfoLogoErrorMessage: 'Veuillez importer un logo valide',
    companyInfoLogoThumbnailErrorMessage: 'Veuillez importer une vignette de logo valide',
    companyInfoLookUpNoMatches: 'Aucun résultat',
    companyInfoSelectValuePlaceholder: 'Sélectionnez une valeur',
    companyInfoHelpSupportMessage: 'Pour obtenir de l\'aide, veuillez contacter',
    companyInfoVersion: 'Version {version}',
    companyInfoTitle: 'Informations sur la société',
    companyInfoFileUploadFailed: 'L’importation du fichier {fileName} a échoué.',
    //Currency Messages
    currencyHeaderText: 'Devise',
    currencyDefaultMessage: 'Devise du système',
    currencyDescription: 'Sélectionnez la devise à utiliser dans votre application.',
    baseCurrencyLabel: 'Devise de base',
    //diary calendar messages
    diaryCalendarHeading: 'Journal',
    diaryCalendarSubHeading: 'Personnalisez votre année en définissant un modèle de travail standard et des jours non travaillés tels que les jours fériés et les fermetures des bureaux.',
    diaryCalendarCustomDayTitle: 'Ajouter un jour personnalisé',
    diaryCalendarCustomPeriodTitle: 'Ajouter une période personnalisée',
    diaryCalendarSelectDateRangeRequiredMsg: 'Veuillez sélectionner une plage de dates',
    diaryCalendarCustomPeriodRequiredMsg: 'Le nom de la période personnalisée est requis',
    diaryCalendarWorkPatternRequiredMsg: 'Veuillez sélectionner un modèle de travail',
    diaryCalendarSelectDateRequiredMsg: 'Veuillez sélectionner une date',
    diaryCalendarCustomDayRequiredMsg: 'Le nom du jour personnalisé est requis',
    diaryCalendarDayTypeRequiredMsg: 'Veuillez sélectionner un type de jour',
    diaryCalendarReplaceCustomPeriodMsg: 'Voulez-vous remplacer « {overlappingPeriods} » par une nouvelle période personnalisée ?',
    diaryCalendarReplaceCustomDayMsg: 'Voulez-vous remplacer « {overlappingDayName} » par un nouveau jour personnalisé ?',
    //for confirmation modal popup need to change later
    diaryCalendarSaveHistoricalDataAlert: 'Voulez-vous vraiment enregistrer les modifications apportées aux données historiques ?',
    diaryCalendarSavePastDayChangesMsg: 'Vos modifications peuvent affecter les calculs des réservations passées. Vous ne pouvez pas annuler cette opération.',
    diaryCalendarStandardWorkPatternLabel: 'Modèle de travail standard',
    diaryCalendarCustomDaysGridHeading: 'Jours personnalisés',
    diaryCalendarCustomPeriodsGridHeading: 'Périodes personnalisées',
    diaryCalendarCustomDaysAddBtn: 'Ajouter un jour personnalisé',
    diaryCalendarCustomPeriodsAddBtn: 'Ajouter une période personnalisée',
    diaryCalendarCustomDayNamePlaceholder: 'Nom du jour personnalisé',
    diaryCalendarCustomPeriodNamePlaceholder: 'Nom de la période personnalisée',
    diaryCalendarCustomDayTypePlaceholder: 'Type de jour du jour personnalisé',
    diaryCalendarCustomWorkPatternPlaceholder: 'Modèle de travail de la période personnalisée',
    diaryCalendarCustomDayIsInUseMessage: 'Chevauche un jour personnalisé existant',
    diaryCalendarCustomPeriodIsInUseMessage: 'Chevauche une période personnalisée existante',
    diaryCalendarCustomGridDateRequiredMessage: 'Veuillez indiquer la date',
    diaryCalendarCustomGridRangeRequiredMessage: 'Veuillez indiquer une plage de dates',
    diaryCalendarCustomGridNameRequiredMessage: 'Veuillez indiquer le nom',
    diaryCalendarCustomGridDayTypesRequiredMessage: 'Veuillez indiquer le type de jour',
    diaryCalendarCustomGridWorkPatternsRequiredMessage: 'Veuillez indiquer le modèle de travail',
    diaryCalendarTotalHourSumErrorMessage: 'Le nombre total d’heures doit être inférieur ou égal à 24 heures',
    diaryCalendarHoursAginstDurationError: 'Les heures de début et de fin doivent inclure le nombre d\'heures attribuées',
    diaryCalendarCustomDayPlaceholder: 'Veuillez saisir un nom de jour personnalisé',
    diaryCalendarDayTypePlaceholder: 'Sélectionnez le type de jour',
    diaryCalendarCustomPeriodPlaceholder: 'Veuillez saisir un nom de période personnalisée',
    diaryCalendarWorkPatternPlaceholder: 'Sélectionnez le modèle de travail',
    diaryCalendarRangeDateLabel: 'Dates de début et de fin',
    diaryCalendarPatternLabel: 'Modèle',
    diaryNameRequiredMessage: 'Veuillez indiquer un nom de journal',
    uniqueDiaryNameMessage: 'Veuillez saisir un nom de journal unique',
    diaryWarningTitle: 'Impossible de supprimer le calendrier du journal',
    diaryWarningMessage: 'Impossible de supprimer le journal « {diaryNames} ». Ce journal est attribué à la ressource.',
    //button labels
    confirmButtonLabel: 'Confirmer',
    cancelButtonLabel: 'Annuler',
    saveButtonLabel: 'Enregistrer',
    okButtonLabel: 'OK',
    //table headings
    diaryCalendarCustomDaysDateTableHeading: 'Date',
    //same value need to check
    diaryCalendarCustomDaysNameTableHeading: 'Nom',
    diaryCalendarCustomPeriodsRangeTableHeading: 'Dates de début et de fin',
    diaryCalendarCustomPeriodsWorkPatternTableHeading: 'Modèle de travail',
    //work pattern
    workPatternHeading: 'Modèles de travail',
    workPatternCommandBarFieldName: 'Modifier le nom du modèle de travail',
    workPatternSubHeading: 'Vous pouvez créer un modèle de travail en sélectionnant un jour de départ et des types de jours spécifiques. Ici, vous pouvez ajouter, modifier ou supprimer des modèles de travail.',
    //for confirmation modal popup need to change later
    workPatternSaveInUseAlert: 'Voulez-vous vraiment enregistrer les modifications apportées au modèle de travail en cours d’utilisation ?',
    workPatternSaveInUseChangesMessage: 'Vos modifications peuvent affecter le calendrier du journal. Vous ne pouvez pas annuler cette opération.',
    workPatternAddButton: 'Ajouter un jour',
    workPatternReqValidation: 'Veuillez sélectionner un type de jour',
    workPatternUniqueMsg: 'Veuillez saisir un nom de modèle de travail unique',
    workPatternReqdMsg: 'Veuillez indiquer un nom de modèle de travail',
    workPatternStartDayLabel: 'Jour de début',
    workPatternDayTableHead: 'Jour',
    workPatternWarningMessage: 'Pour le supprimer, supprimez-le des journaux.',
    workPatternWarningTitle: 'Vous ne pouvez pas supprimer le modèle de travail {selection} car il est en cours d’utilisation.',
    //Day Types
    dayTypePageHeading: 'Types de jour',
    dayTypeHeading: 'Type de jour',
    dayTypeCommandBarFieldName: 'Modifier le jour du modèle de travail',
    dayTypeSubHeading: 'Configurez le type (jour ouvré ou jour non travaillé) pour représenter les horaires habituels de votre entreprise.',
    //for confirmation modal popup need to change later
    dayTypeSaveInUseAlert: 'Voulez-vous vraiment enregistrer les modifications apportées au type de jour en cours d’utilisation ?',
    dayTypeSaveInUseChangesMessage: 'Vos modifications peuvent affecter le calendrier du journal. Vous ne pouvez pas annuler cette opération.',
    dayTypeWorkingHoursRequiredMessage: 'Veuillez saisir le temps de travail',
    dayTypeContingencyTimeRequiredMessage: 'Veuillez saisir le temps supplémentaire',
    dayTypeWorkingHoursCannotZeroMessage: 'Le temps de travail ne peut pas être de 00:00',
    dayTypeWorkDayLabel: 'Jour ouvré',
    dayTypeNonWorkDayLabel: 'Jour non travaillé',
    dayTypeWorkTimeLabel: 'Temps de travail',
    dayTypeContingencyTimeHours: 'Temps supplémentaire',
    dayTypeTitleRequiredMessage: 'Veuillez indiquer le nom du type de jour',
    dayTypeTitleUniqueMessage: 'Veuillez saisir un type de jour unique',
    dayTypeWarningTitle: 'Vous ne pouvez pas supprimer le type de jour {selection} car il est en cours d’utilisation.',
    dayTypesWarningMessage: 'Pour le supprimer, retirez-le d’abord du modèle de travail.',
    //entity import
    entityImportPageHeader: 'Importer des données',
    entityImportPageSummaryText: 'Importez des données dans votre application en deux étapes simples :<ol><li>Téléchargez le modèle correspondant et saisissez-y les données en veillant à renseigner les champs obligatoires.</li><li>Importez le modèle que vous avez rempli.</li></ol>Les données que vous avez saisies dans le modèle seront importées dans l\'application.',
    entityImportDownloadTemplatesHeader: 'Téléchargez un modèle',
    entityImportDownloadTemplatesNumber: '1',
    entityImportUploadDataHeader: 'Importez le modèle que vous avez rempli',
    entityImportUploadDataNumber: '2',
    entityImportJobLabel: 'Missions',
    entityImportClientLabel: 'Clients',
    entityImportResourceLabel: 'Ressources',
    entityImportSkillLabel: 'Compétences',
    entityImportUploadControlText: 'Cliquez sur un fichier ou faites-le glisser dans cette zone pour l’importer',
    entityImportSelectUploadFileLabel: 'Sélectionnez un fichier à importer',
    entityImportImportSuccessful: 'Importation réussie',
    entityImportImportUnsuccessful: 'L’importation a échoué',
    entityImportTemplateDownloadFailed: 'impossible de télécharger le modèle',
    entityImportUploadControlError: 'Veuillez sélectionner le fichier à importer',
    entityImportUploadDropDownError: 'Veuillez sélectionner le type',
    entityImportUploadDropDownPlaceholder: 'Choisissez un type de données',
    entityImportFileUploadFailed: 'l’importation du fichier a échoué.',
    entityImportTypeOfData: 'Type de données',
    entityImportUploadAndVerify: 'Importer et vérifier',
    entityImportCancelBtn: 'Annuler',
    entityImportConfirmImport: 'Confirmer l\'importation',
    entityImportFormValidateMsg: 'Cliquez pour pré-valider avant l’importation',
    entityImportmailSubject: 'Conserver la limite de licence cloud',
    entityImportUploadProcessed: '{EntriesProcessedCnt} entrées {currentEntityType} traitées.',
    entityImportTemplateFileName: 'EntityImportTemplate',
    processFormErrorCorrectionText: 'Pour terminer l’importation, veuillez corriger les erreurs suivantes.',
    processFormAlternateOption: 'Vous pouvez également choisir de les supprimer ou d\'annuler la mise à jour, modifier le fichier Microsoft Excel et l’importer à nouveau.',
    processFormRowNoFromExcelMsg: 'Le numéro de ligne correspondant dans le fichier Microsoft Excel est indiqué ci-dessous.',
    processFormRequiredClientNameField: 'Le champ Nom du client est obligatoire.',
    processFormRequiredClientCodeField: 'Le champ Code client est obligatoire.',
    processFormRequiredJobTitleField: 'Le champ Intitulé du poste est obligatoire.',
    processFormRequiredSkillNameField: 'Le champ Nom de la compétence est obligatoire.',
    processFormRequiredSkillInfoField: 'Le champ Informations sur la compétence est obligatoire.',
    processFormRequiredSkillSectionField: 'Le champ Section de compétence est obligatoire.',
    processFormRequiredFirstNameField: 'Le champ Prénom est obligatoire.',
    processFormRequiredLastNameField: 'Le champ Nom est obligatoire.',
    processFormRequiredEmailField: 'Le champ E-mail est obligatoire.',
    processFormProcessed: ' traité(s).',
    processFormProcessedWithError: ' traité(s) avec des erreurs.',
    processFormProcessedWithNoError: ' traité(s) sans erreur.',
    processFormWithError: ' avec des erreurs',
    processFormWithNoError: ' sans erreur',
    processFormLicenseUserContError: 'Ces entrées {currentEntityType} ne peuvent pas être importées.',
    processFormLicenseUserDivLine1: 'Vos licences prennent en charge jusqu’à <b>{allowedActiveUsersCount}</b> utilisateurs actifs.',
    processFormLicenseUserDivLine2_1: 'Après cette importation, il y aura {totalRecordsProcessed} utilisateurs actifs.',
    processFormContactUs: '{contactUs}',
    processFormLicenseUserDivLine2_2: 'pour augmenter votre limite.',
    processFormLicenseUserContErrorAlert: 'Un problème est survenu. L\'importation ne peut pas être traitée',
    processFormContactUsText: 'Contactez-nous',
    //color scheme
    colourSchemeHeader: 'Thème de couleur',
    colourSchemeFieldName: 'Modifier le nom du thème de couleurs',
    colourSchemeHeaderAddButtonText: 'Ajouter une règle de couleur',
    colourSchemeSummaryText: 'Affichez chaque type de réservation dans une couleur différente en sélectionnant votre couleur préférée pour chaque champ concerné.',
    colourSchemeRolesSummaryText: 'Affichez les rôles dans une couleur différente en sélectionnant votre couleur préférée pour chaque champ concerné.',
    colourSchemeConfirmModalText: 'La modification du champ supprimera toutes les règles de thème de couleur existantes. Continuer ?',
    colourSchemeTableRequired: 'Veuillez sélectionner un tableau',
    colourSchemeFieldRequired: 'Veuillez sélectionner un champ',
    colourSchemeGridEmptyText: 'Aucune règle de couleur n\'a été créée',
    colourSchemeGridAddButtonText: 'Ajouter une règle',
    colourSchemePreviewText: 'Aperçu du texte',
    colourSchemeFieldValueRequired: 'Veuillez sélectionner la valeur du champ dans la liste',
    colourSchemeFieldValueUnique: 'La valeur du champ doit être unique',
    colourSchemeLookUpNoMatches: 'Aucun résultat',
    colourSchemeSelectValuePlaceholder: 'Sélectionnez une valeur',
    colourSchemeResourceLookupPlaceholder: 'Non attribué',
    colourSchemeColourSchemeAddButton: 'Ajouter une règle de couleur',
    colourSchemeTable: 'Tableau',
    colourSchemeField: 'Champ',
    colourSchemePreviewTextTitle: 'Aperçu',
    colourSchemeColourCodeTextTitle: 'Couleur',
    colourSchemeCreateColorTheme: 'Créer un thème de couleur',
    colourSchemeFieldDropdownPlaceholder: 'Choisissez un champ',
    colourSchemeTableDropdownPlaceholder: 'Aucun',
    colorSchemeUniqueTitle: 'Veuillez saisir un nom de thème de couleur unique',
    colorSchemeRequiredTitle: 'Veuillez indiquer un nom de thème de couleur',
    colourThemeTabTitle_Bookings: 'Réservations',
    colourThemeTabTitle_Roles: 'Rôles',
    colourSchemeDescriptionPlaceholder: 'Description de la couleur',
    //conflicts
    conflictPageHeader: 'Conflits',
    conflictsMsgsSubHeaderLabel: 'Quand afficher les conflits.',
    conflictsMsgsSubLabel_line1: 'Choisissez si vous souhaitez afficher les conflits. Sélectionnez le seuil à partir duquel l\'affectation d’une ou plusieurs réservations à une même ressource apparaîtra comme un conflit.',
    conflictsMsgsResourceLoadingControlLabel: 'Lorsque la charge de la ressource est d’au moins',
    conflictsMsgsShowConflictsLabel: 'Afficher les conflits',
    conflictsMsgsConfermationModelHeader: 'Veuillez saisir un seuil de charge valide.',
    conflictsMsgsConfermationModelSubHeader: 'Le seuil de charge doit être compris entre {minValue} et {maxValue} %.',
    conflictsMsgsShowResourceLoadingNote: 'Les valeurs de charge acceptées vont de {minValue} à {maxValue} %.',
    conflictsMsgsOkBtn: 'OK',
    conflictsMsgsYesText: 'Oui',
    conflictsMsgsNoText: 'Non',

    //Service accounts
    serviceAccounts: {
        maximumFieldLengthValidationMessage: '{maximumFieldSize} caractères maximum',
        pageHeader: 'Gestion de compte de service',
        addEntity: 'Ajouter un compte de service',
        saveButtonLabel: 'Enregistrer les modifications',
        cancelButtonLabel: 'Annuler',
        markedForDeletionMessage: 'Marqué pour suppression. La suppression aura lieu lorsque vous confirmerez les modifications.',
        cancelDeletion: 'Annuler la suppression',
        serviceAccountManagementTitle: 'Gestion de compte de service',
        serviceAccountsSubTitle: 'Comptes de service actifs',
        serviceAccountDescription: 'Vous voulez développer quelque chose pour intégrer et étendre Retain ? Ajouter, modifier ou supprimer des comptes de service ici.',
        serviceAccountNavigateToRetainApiDocumentationMessage: 'Vous pouvez également en apprendre davantage sur l\'<linkText>API Retain Cloud</linkText> dans notre documentation pratique.',
        serviceAccountManagementUsedAccountsWarning: 'Vous approchez de la limite des 5 comptes de service ajoutés. Gérez les comptes existants ou supprimez ceux qui ne sont pas utilisés.',
        serviceAccountManagementUsedAccountsError: 'Vous avez atteint la limite de 5 comptes de service ajoutés. Gérez les comptes existants ou supprimez ceux qui ne sont pas utilisés.',
        emptyStateMessage: 'Aucun résultat',
        setPassword: 'Définir un mot de passe',
        password: 'Mot de passe',
        name: 'Nom',
        tenant: 'Client',
        securityProfile: 'Profil de sécurité',
        email: 'E-mail',
        savePasswordTooltip: 'Veuillez enregistrer les modifications avant de définir un mot de passe',
        nameValidationMessage: 'Saisissez un nom',
        emailValidationMessage: 'Saisissez votre e-mail',
        typeHerePlaceholder: 'Saisissez ici',
        nameColumnTitle: 'Nom',
        emailColumnTitle: 'E-mail',
        securityProfileColumnTitle: 'Profil de sécurité',
        actionsColumnTitle: 'Actions',
        emailExplanation: 'Saisissez une adresse e-mail unique qui n\'est utilisée par aucun compte existant.',
        formHasErrorsMessage: 'Ce formulaire contient des erreurs'
    },

    //workflows
    workflowsSettings: {
        roleByNameWorkflowPageHeader: 'Rôles par nom',
        roleByRequirementsWorkflowPageHeader: 'Rôles par critère',
        rolesByNamePageDescriptionLabel: 'Les rôles peuvent être utilisés pour demander une ressource sur un emploi. Les rôles par nom sont utilisés lorsque vous connaissez la ressource souhaitée.',
        rolesByRequirementsPageDescriptionLabel: 'Les rôles peuvent être utilisés pour demander une ressource sur un emploi. Les rôles par critère vous permettent de soumettre des critères pour trouver une ressource correspondante.',
        draftStateDescription: 'Les rôles qui ne sont pas prêts à être soumis en tant que demandes peuvent être sauvegardés comme ébauche.',
        requestedStateDescription: 'Les rôles qui peuvent être rendus actifs en tant que réservés, ou rejetés.',
        liveStateDescription: 'Les rôles qui ont été réservés.',
        rejectedStateDescription: 'Les rôles qui ont été rejetés.',
        archivedStateDescription: 'Les rôles qui ne nécessitent plus d\'action.',
        statesLegendTitle: 'Les états de déroulement du travail',
        actorsSectionTitle: 'Les intervenants dans le déroulement du travail',
        actorsSectionDescription: 'Définissent ce que chaque intervenant peut faire dans le déroulement du travail.',
        requesterActorTitle: 'Le demandeur',
        roleByNameRequesterActorDescription: 'Crée des rôles pour soumettre  des demandes d\'emploi.',
        roleByRequirementsRequesterActorDescription: 'Crée des rôles pour soumettre des demandes comportant un ensemble de critères.',
        whoCanCreateRolesLabel: 'Toute personne peut créer des rôles pour',
        roleByRequirementsWhoCanCreateRolesLabel: 'Toute personne peut créer des rôles sur',
        whoCanCreateRolesForThemselvesLabel: 'Toute personne peut créer des rôles pour elle-même sur',
        creatorActionsInfoBannerLabel: 'Les actions ci-dessous sont uniquement disponibles pour le concepteur du rôle..',
        deleteRolesWithAssigneesInfoBannerLabel: 'Pour supprimer des rôles avec plusieurs attributaires, l\'utilisateur doit remplir cette condition pour tous les attributaires.',
        draftRolesActionsTitle: 'Brouillon',
        requesterCanEditDeleteLabel: 'Modifier et supprimer les rôles provisoires',
        requesterCanSubmitLabel: 'Envoyer une demande',
        requesterCanArchiveLabel: 'Archive',
        requestedRolesActionsTitle: 'Demandé',
        requesterCanRestartRequestedLabel: 'Redémarrer',
        requesterCanDeleteRequestedLabel: 'Supprimer les rôles demandés',
        restartingActionsTitle: 'Redémarrage',
        requesterCanRestartRejectedLabel: 'Relancer les rôles rejetés',
        requesterCanRestartArchivedLabel: 'Relancer les rôles archivés',
        completedRolesActionsTitle: 'Rôles achevés',
        requesterCanDeleteLiveLabel: 'Supprimer les rôles actifs',
        requesterCanDeleteRejectedLabel: 'Supprimer les rôles rejetés',
        requesterCanDeleteArchivedLabel: 'Supprimer les rôles archivés',
        assignerActorTitle: 'Assignateur',
        assignerActorDescription: 'affecte les ressources aux rôles demandés en fonction des critères.',
        assignerWhoCanRespondLabel: 'peut affecter des rôles',
        assignerCanAssignResourcesLabel: 'Affecter des ressources aux rôles',
        approverActorTitle: 'Approbateur',
        approverActorDescription: 'Répond aux demandes.',
        appproverWhoCanRespondLabel: 'peut répondre aux demandes de',
        approverCanMakeLiveLabel: 'Mise en ligne',
        criteriaRoleCanMakeLiveLabel: 'Activer un rôle ou un attributaire',
        approverCanRejectLabel: 'Rejeter',
        criteriaRoleCanRejectLabel: 'Rejeter un rôle ou un attributaire',
        approverCanRestartLabel: 'Redémarrer',
        approverCanDeleteRequestedLabel: 'Supprimer les rôles demandés',
        approverCanDeleteLiveLabel: 'Supprimer les rôles actifs',
        approverCanDeleteRejectedLabel: 'Supprimer les rôles rejetés',
        approverCanDeleteArchivedLabel: 'Supprimer les rôles archivés',
        approverSelectedResourcesInvalidValue: 'Max ${maxLimitCount} ressources. Si plus d\'approbateurs sont requis, utilisez plutôt \'Profils de sécurité sélectionnés',
        assignerSelectedResourcesInvalidValue: 'Max ${maxLimitCount} ressources. Si plus d\'assignateurs sont requis, utilisez plutôt \'Profils de sécurité sélectionnés',
        requesterSelectedResourcesInvalidValue: 'Un maximum de ${maxLimitCount} ressources. Si davantage de demandeurs sont nécessaires, envisagez d\'utiliser les « profils de sécurité sélectionnés » à la place.',
        selectedSecurityProfilesInvalidValue: 'Maximum de ${maxLimitCount} profils de sécurité',
        addAnotherPrefix: 'Ajouter un',
        noResultsMessagePrefix: 'Non',
        noResultsMessageSuffix: 'a été trouvé avec ce nom.',
        multiValueFieldErrorMessagePrefix: 'Sélectionner au moins un',
        saveButtonLabel: 'Sauvegarder les modifications',
        cancelButtonLabel: 'Annuler',
        formHasErrorsMessage: 'Le formulaire contient des erreurs',
        noResultsFoundMessage: 'Pas de résultats trouvés',
        roleCreatorLabel: 'Le créateur de rôle et',
        whoCanActAsRequesterLabel: 'peuvent agir en tant que demandeurs d\'un rôle.'
    },
    //report settings
    reportSettingsSubLabel: 'Définissez des objectifs pour votre organisation',
    reportSettingsErrorTitle: 'Valeur cible incorrecte',
    reportSettingsBillabillityLabel: 'Utilisation facturable cible globale',
    reportSettingsBillabilityRule: 'Définir une cible globale pour comparer les taux réels d\'utilisation facturabl',
    reportSettingsJobOpportunityLabel: 'Seuil d\'opportunité de mission',
    reportSettingsJobOpportunityRule: 'Pourcentage d\'opportunité minimum pour compter une mission comme facturable',
    reportSettingsFieldTitle: 'Utilisation facturable',

    //Colour schemes
    deleteColourSchemeTitle: 'Supprimer le thème de couleur « {itemTobeDeleted} » ?',
    deleteColourSchemeInformationMessage: 'Tous les plans qui utilisent ce thème reviendront au thème par défaut. ',
    deleteColourSchemeWarningMessage: 'Cette action ne peut pas être annulée.',
    colourSchemeCheckMessage: 'Je comprends l\'impact de la suppression de ce thème de couleur.',
    deleteColourSchemePrimaryButton: 'Supprimer le thème de couleur',
    deleteColourSchemeSecondaryButton: 'Conserver le thème de couleur',

    colorPickerText: 'Sélecteur de couleur',

    //security Profile
    securityProfilePageHeader: 'Profils de sécurité',
    securityProfileFieldName: 'Modifier le nom du profil de sécurité',
    functionalAccessHeading: 'Général',
    functionalAccessSubHeading: 'Contrôle les pages et les fonctionnalités auxquelles ce profil de sécurité a accès. Par exemple, vous pouvez utiliser une règle Accès fonctionnel pour bloquer l\'accès aux paramètres d\'administration.',
    yesLabel: 'Oui',
    noLabel: 'Non',
    entityAccessSubHeading: 'Contrôlez le niveau d\'accès de ce profil de sécurité aux {entityName}s.',
    skillEntitySubHeading: 'Contrôlez le niveau d\'accès de ce profil de sécurité aux {entityName}.',
    entityAccessSubHeadingRemaining: 'Activez ou désactivez l\'accès, ou bien définissez des niveaux d’accès pour un contrôle plus précis.',
    readEntityResource: 'Les {entityName}s sont toujours visibles pour les utilisateurs mais vous pouvez restreindre la visibilité de certains {entityName}s à l\'aide des commandes ci-dessous {lineBreak} La sécurité de lecture supplantera toutes les autres règles de sécurité.',
    readEntityJob: 'Les {entityName}s sont toujours visibles pour les utilisateurs mais vous pouvez restreindre la visibilité de certains {entityName}s à l\'aide des commandes ci-dessous {lineBreak} Les utilisateurs pourront toujours consulter les emplois pour lesquels ils sont inscrits. La sécurité de lecture supplantera toutes les autres règles de sécurité.',
    readEntityBooking: 'Les {entityName}s sont visibles en fonction des conditions de lecture définies pour les éléments Job [Poste] et Resource [Ressource]. La sécurité de lecture aura priorité sur toutes les autres règles de sécurité.',
    readEntityRole: 'Les {entityName}s sont visibles en fonction des conditions de lecture définies pour les éléments Job [Poste] et Resource [Ressource]. La sécurité de lecture aura priorité sur toutes les autres règles de sécurité.',
    readEntityScenario: 'Les {entityName}s sont toujours visibles pour tous les utilisateurs. La sécurité de lecture aura priorité sur toutes les autres règles de sécurité.',
    readEntityRoleRequest: 'Les {entityName} sont toujours visibles par tous les utilisateurs. La sécurité de la lecture supplantera toutes les autres règles de sécurité.',
    readEntitySkill: 'Les compétences et les certifications sont toujours visibles pour les utilisateurs, mais vous pouvez restreindre celles qu\'ils peuvent ajouter à leur propre profil à l’aide des commandes ci-dessous.',
    readEntityClient: 'Les {entityName} sont toujours visibles par tous les utilisateurs. La sécurité de la lecture supplantera toutes les autres règles de sécurité.',
    createEntity: 'Désactiver cette option masquera les options de l\'interface et empêchera ce profil de sécurité de créer des {entityName}s.',
    editEntity: 'Désactiver cette option masquera les options de l\'interface et empêchera ce profil de sécurité de modifier des {entityName}s.',
    deleteEntity: 'Désactiver cette option masquera les options de l\'interface et empêchera ce profil de sécurité de supprimer des {entityName}s.',
    customConditionsAreaHeader: '{entityName}s qui...',
    liveRoleSubHeading: 'Rôles actifs',
    liveRoleBookingMessage: 'La création de réservations actives à partir de rôles est contrôlée par les autorisations de réservation, veuillez consulter ',
    workflowCardSubHeading: 'Flux de travail des rôles',
    workflowCardMessage: 'Les paramètres de flux de travail des rôles vous permettent de spécifier qui peut demander, approuver et exécuter d\'autres actions portant sur les rôles.',
    workflowCardBtnText: 'Afficher le flux de travail des rôles',
    workflowTurnedOff: 'Activez l\'option Workflows [Flux de travail] à l\'onglet General [Général] de votre profil de sécurité pour accéder à cette page.',
    subRuleCreateRequest: 'Désactiver cette option masquera les options de l\'interface et empêchera ce profil de sécurité de créer des demandes.',
    subRuleRejectRequest: 'Désactiver cette option masquera les options de l\'interface et empêchera ce profil de sécurité de rejeter des demandes.',
    subRuleAssignCriteriaRoles: 'Désactiver cette option masquera les options de l\'interface et empêchera ce profil de sécurité d\'attribuer des ressources suggérées aux rôles de critères.',
    readRuleCondition: 'Quels {entityName}s peut-il voir ?',
    createRuleCondition: 'Quels {entityName} peut-il créer ?',
    skillReadRuleCondition: 'Quelles compétences et certifications peuvent-ils ajouter à leur propre profil ?',
    editRuleCondition: 'Quels {entityName} peut-il modifier ?',
    deleteRuleCondition: 'Quels {entityName} peut-il supprimer ?',
    securityProfileNameRequiredMsg: 'Veuillez indiquer un nom de profil de sécurité',
    uniqueSecurityProfileNameMsg: 'Veuillez saisir un nom de profil de sécurité unique',
    delete_failureWarningTitle: 'Impossible de supprimer le profil de sécurité',
    delete_failureWarningMessage: 'Impossible de supprimer le profil de sécurité {profileName}. Ce profil de sécurité est en cours d\'utilisation.',
    delete_failureButtonLabel: 'OK',
    fieldSecurityHeading: 'Champs de {entityName}',
    fieldSecuritySubHeading: 'Définissez les champs de {entityName} avec lesquels ce profil de sécurité peut interagir. Par défaut, les champs sont modifiables, mais ils peuvent également être passés en lecture seule ou masqués.',
    fieldSecurityInfoForCondition: 'Pour',
    fieldSecurityInfoThisFieldIsCondition: 'Ce champ est',
    fieldSecurityInfoOtherwiseCondition: 'Sinon le champ est',
    mandatoryNotification: ' Ce champ est obligatoire. Restreindre un champ obligatoire peut avoir des résultats inattendus.',
    editAccessNotification: ' L\'accès en modification a été désactivé pour les {entityName}s. Sélectionner « Modifiable » n\'aura aucun effet sur l\'accès au champ.',
    readOnlyNotification: 'Ceci est un champ système en lecture seule, il ne peut pas être modifiable',
    externalIdNotificationMessage: 'Ce champ est modifiable uniquement via le portail API',
    note: 'Remarque',
    important: 'Importante',
    emptyFieldSecurityViewMessage: 'Aucun champ n\'a encore été ajouté',
    accessLevel: 'Niveau d\'accès',
    tabMessage: 'onglet {tabName}',
    skillCategoriesLabel: 'Catégories de compétences',
    departmentLabel: 'Départements',
    divisionLabel: 'Divisions',
    skillEntityTypeLabel: 'Types de compétences',
    serviceLineLabel: 'Lignes de service',
    skillsCertificationLabel: 'Compétences et certifications',
    viewbudgetEntity: 'Permettre aux utilisateurs de visualiser les valeurs budgétaires estimées sur les rôles et les valeurs réelles une fois les ressources attribuées',
    managerApprovalAlert: 'FR_Users must have a manager set in \'Reports to\' field or they will not be able to update their skills_FR',
    managerApprovalSubHeading: 'FR_For users with this security profile any changes they make to their own skills will need to be approved by their manager. Skill preferences can be changed without approval_FR',
    customConditions: {
        operators: {
            Int: {
                LessThan: 'Inférieur à',
                LessThanOrEqual: 'Inférieur ou égal à',
                Equals: 'Égal à',
                GreaterThanOrEqual: 'Supérieur ou égal à',
                GreaterThan: 'Supérieur à',
                NOT_EQUALS_OPERATOR: 'N\'est pas égal à'
            },
            DateTime: {
                LessThanOrEqual: 'Avant',
                GreaterThanOrEqual: 'Après'
            },
            ID: {
                IN_OPERATOR: 'Est un des',
                NOT_IN_OPERATOR: 'Ne fait pas partie de'
            },
            Bool: {
                Equals: 'Égal à'
            }
        },
        valueTypes: {
            relativeToToday: 'Par rapport à aujourd\'hui',
            blank: 'Vide',
            selectedValues: 'Valeurs sélectionnées',
            loggedInUserValue: 'Valeur utilisateur connecté',
            existingValue: 'Valeur existante',
            customValue: 'Valeur personnalisée'
        },
        relativeDateValues: {
            PLUS_180: 'Aujourd\'hui +180 jours',
            PLUS_90: 'Aujourd\'hui +90 jours',
            PLUS_28: 'Aujourd\'hui +28 jours',
            PLUS_7: 'Aujourd\'hui +7 jours',
            PLUS_1: 'Demain',
            TODAY: 'Aujourd\'hui',
            MINUS_1: 'Hier',
            MINUS_7: 'Aujourd\'hui -7 jours',
            MINUS_28: 'Aujourd\'hui -28 jours',
            MINUS_90: 'Aujourd\'hui -90 jours',
            MINUS_180: 'Aujourd\'hui -180 jours'
        },
        noConditionOperatorError: 'Veuillez sélectionner l\'opérateur',
        noConditionValueTypeError: 'Veuillez sélectionner le type de valeur',
        addConditionsListButtonLabel: '+ Ajouter condition ET',
        andOperatorLabel: 'ET',
        maxConditionsCountLabel: ' 3 maximum',
        addConditionRowButtonLabel: '+ Ajouter condition OU',
        orOperatorLabel: 'OU',
        fieldHeaderLabel: 'Domaine',
        operatorHeaderLabel: 'Opérateur',
        valueTypeHeaderLabel: 'Type de valeur',
        valueHeaderLabel: 'Valeur',
        noResultsFoundMessage: 'Aucun résultat trouvé',
        pleaseEnterFieldLabel: 'Veuillez saisir le domaine',
        conditionFieldNamePlaceholder: 'Sélectionnez le domaine...',
        yesLabel: 'Oui',
        noLabel: 'Non',
        wholeNumberInputError: 'Veuillez saisir une valeur valide',
        commonPredefinedConditionsLabel: 'Utiliser une condition prédéfinie',
        inheritReadPredefinedConditionsLabel: 'Utiliser uniquement les conditions de lecture définies pour les emplois et les ressources',
        commonCustomConditionLabel: 'Créer une condition personnalisée',
        inheritReadCustomConditionLabel: 'Ajouter une condition personnalisée en plus des conditions de lecture définies pour les emplois et les ressources.',
        addAnotherPrefix: 'Ajouter',
        deleteRowLabel: 'Supprimer la ligne',
        pleaseEnterValueLabel: 'Veuillez saisir la valeur',
        noResultsMessagePrefix: 'Non',
        noResultsMessageSuffix: 'a été trouvé avec ce nom.',
        jsonConditionLabel: 'Spécifier un paramètre JSON personnalisé',
        jsonConditionWarningBannerText: 'Les paramètres JSON personnalisés doivent être utilisés avec précaution car ils peuvent entraîner une baisse des performances.\nLa mauvaise configuration des règles JSON peut entraîner des problèmes d\'instabilité.',
        invalidJsonErrorMessage: 'Vérifiez que le JSON est valide.',

        apiPortalInfoText: 'Validez automatiquement les paramètres JSON en cliquant hors de la zone de texte',
        apiPortalLabel: 'Portail API.',
        inheritReadJsonConditionLabel: 'Spécifier un paramètre JSON personnalisé en plus des conditions de lecture définies pour les emplois et les ressources.',
        //Skills
        addSkillHeader: 'Ajouter des compétences et des certifications à la page de profil',
        allSkillsLabel: 'Toutes les compétences et certifications',
        onlyTheseSkillsLabel: 'Uniquement celles-ci',
        onlyRelatedSkillsLabel: 'Uniquement les compétences et certifications liées à celles-ci',
        emptySkillErrorMessage: 'Veuillez sélectionner au moins une valeur',
        emptyJsonConditionErrorMessage: 'Veuillez sélectionner au moins une des options'
    },
    //entitiesConfiguration
    planningDataAliasUseCaseInfo: 'Un alias est un terme plus familier qui peut être utilisé dans votre application. Ajoutez des alias pour rendre les termes peu familiers plus faciles à reconnaître.',
    aliasHeading: 'Alias',
    singularAliasLabel: 'Alias au singulier',
    pluralAliasLabel: 'Alias au pluriel',
    BookingDescription: 'Une réservation représente l’allocation d\'une ressource à une mission pour une plage de dates spécifique, et correspond à un nombre d\'heures mesurable. Vous pouvez créer des réservations pour des missions telles que de la Gestion de projet ou des Tests, mais aussi pour des vacances ou des congés de maladie. Chaque réservation doit être associée à une mission.',
    JobDescription: 'Une mission est un projet, une tâche ou un travail qui nécessite une ressource. Vous pouvez créer une mission pour des tâches telles que de la Gestion de projet ou des Tests, mais aussi pour des congés ou des congés de maladie.',
    ResourceDescription: 'Les ressources désignent les matériaux, le personnel ou d’autres actifs qu’une organisation utilise pour fonctionner efficacement. Tout élément qui possède une capacité finie pouvant effectuer une mission peut être considéré comme une ressource. Le personnel, les salles de formation et les machines sont des exemples de ressources.',
    ClientDescription: 'Les clients désigne les clients de votre organisation. Un client peut avoir plusieurs missions.',
    rolerequestgroupDescription: 'scénario est un ensemble de rôles pour une mission donnée.',
    rolerequestDescription: 'Un rôle est le précurseur d\'une ou plusieurs réservations. Il peut être créé en fonction d’une ressource ou de certains critères.',
    DepartmentDescription: 'Un département est un segment d\'une organisation spécialisé dans un processus métier particulier, souvent composé de ressources ayant des compétences et des responsabilités similaires',
    DivisionDescription: 'Une division est une unité commerciale ou un segment de haut niveau dans une organisation ; elle est composée de plusieurs départements',
    //Skill Types Message
    //Levels
    skillPageHeader: 'Compétences et certifications',
    skillTypeLevelsTabTitle: 'Niveaux',
    skillTypeSkillsTabTitle: 'Compétences',
    fieldsTabTitle: 'Champs',
    addSkill: 'Ajouter une compétence',
    addRetainSkillLibrary: 'Ajouter depuis la bibliothèque Retain',
    levelNameDescriptionText: 'Pour ce type de compétence, les niveaux sont appelés',
    levelNameInfoText: '{levelName} 1 est le niveau le plus bas.',
    addLevels: 'Ajouter {levelName}',
    skillTypeLevelNameRequiredMessage: 'Veuillez entrer un nom de niveau',
    whiteSpaceValidation: 'Seuls les espaces blancs ne sont pas autorisés dans l\'option',
    skillLevelRequiredValidation: 'Le nom du niveau est requis',
    skillLevelUniqueValidation: 'Le nom du niveau existe déjà',
    skillLevelNamePlaceholder: 'Nom du niveau',
    skillLevelDescriptionPlaceholder: 'Description du niveau',
    //Skill fields
    skillFieldHeading: 'Champs supplémentaires pour ce type de compétence',
    addFieldButtonLabel: 'Ajouter un champ',
    skillCommandBarFieldName: 'Modifier le nom de la compétence',
    //reusable grid
    cancelDeletion: 'Annuler la suppression',
    markedForDeletion: 'Marqué pour suppression. La suppression aura lieu lorsque vous confirmerez les modifications. ',
    addButtonReusableGrid: 'Ajouter une ligne',
    mandatory: 'Obligatoire',
    markDeleteWarningTitle: 'Voulez-vous vraiment marquer « {recordName} pour suppression ?',
    markDeleteWarningTitleDefault: 'Voulez-vous vraiment marquer cet enregistrement pour suppression ?',
    //Field Properties
    fieldFormattingDisabledTooltipText: 'Ne peut pas être modifié sur les champs intégrés',
    fieldPropetiesAliasDefinition: 'Un alias est un terme plus familier qui peut être utilisé dans votre application.',
    fieldPropetiesChooseAliasMessage: 'Choisissez un alias pour le tableau sélectionné et saisissez des libellés de champs à utiliser dans votre application.',
    noDescriptionMsg: 'Cliquez ici pour ajouter une description',
    configurePageField: 'Champs {pageTitle}',
    descriptionText: 'Description',
    builtInTabsTitle: 'Champs intégrés',
    customFieldTabsTitle: 'Champs personnalisés',
    builtInTabsDescription: 'Ce sont les champs {entity} par défaut intégrés au système. Ils ne peuvent pas être modifiés, mais vous pouvez les masquer si vous n’en avez pas besoin.',
    customFieldTabsDesc: 'Ce sont des champs que vous avez ajoutés à l\'application. Ils seront affichés à côté des champs système {entity}.',
    builtInFieldTab: 'Intégré',
    customText: 'Personnalisé',
    customFieldAddButton: 'Ajouter un champ personnalisé',
    emptyCustomViewMessage: 'Aucun champ personnalisé n\'a encore été ajouté',
    emptyBuiltInViewMessage: 'Aucun champ intégré n\'a encore été ajouté',
    fieldLabelReqdValidation: 'Le libellé du champ ne peut pas être vide',
    uniqueFieldNameValidation: 'La ${label} doit être unique',
    bracketsFieldNameValidation: 'La ${label} ne peut pas contenir [ ou ]',
    lookupFieldInputRequired: 'Le champ de recherche ne peut pas être vide',
    noMatchesText: 'Aucun résultat',
    lookUpField: 'Valeurs issues de',
    lookUpLinkText: 'Configuration des champs de recherche',
    maxCharMessage: '{maxChar} caractères maximum',
    maxCharTagMessage: 'Limite appliquée uniquement aux mots-clés définis par l\'utilisateur. {maxChar} maximum',
    incorrectDecimalFormatMsg: 'Format décimal incorrect (saisissez une valeur numérique avec un maximum de 10 décimales)',
    incorrectTargetBillabilityMessage: 'La valeur minimale autorisée est 0',
    inputBeyondLimitMsg: 'La valeur d\'entrée dépasse la limite',
    noOfChars: 'Nombre de caractères',
    typeText: 'Type',
    labelText: 'Libellé',
    hiddenText: 'Masqué',
    decimalPlacesText: 'Décimales',
    exampleText: 'Exemple',
    lookupValuesLabel: 'Valeurs de recherche',
    newFieldValuesLabel: 'Nom de la nouvelle liste',
    nextLabel: 'Suivant',
    prevLabel: 'Précédent',
    fieldValuesTitle: 'Valeurs de champ',
    fieldValuesSubTitle: 'Spécifier la liste des valeurs de ce champ',
    lookupValuesNotifyMessage: 'Ceci ne peut pas être modifié après la création du champ.{lineBreak} Les valeurs de la liste peuvent être modifiées ultérieurement sur la page « Valeurs ».',
    newFieldNameRequiredMessage: 'Le nom de la valeur de champ est requis',
    fieldAliasRequiredValidation: 'L’alias {fieldAlias} est requis',
    fieldDescriptionRequiredValidation: 'La description {fieldDescription} est requise',
    newFieldValuesRequiredMessage: 'La liste de valeurs du champ ne peut pas être vide',
    multiSelectText: 'Sélection multiple',
    valuesListLabel: 'Liste de valeurs',
    useExistingValuesText: 'Utiliser une liste de valeurs existante',
    createNewValuesListText: 'Créer une nouvelle liste de valeurs',
    createNewValuesSaveInfo: 'La nouvelle liste sera enregistrée dans la page « Valeurs »',
    planningDataSaveInfo: 'Ceci ne peut pas être modifié après la création du champ',
    formattingLabel: 'Format',
    minimumLabel: 'Min',
    maximumLabel: 'Max',
    valuesText: 'Valeurs',
    valueFieldsCommandBarFieldName: 'Modifier le nom de la valeur de champ',
    calculatedMessage: 'Ce champ est calculé automatiquement à partir des données de votre application.',
    rangeLabel: 'Plage',
    systemReadonlyMessage: 'Ce champ est en lecture seule et contient des informations mises à jour par l\'application',
    systemRequiredMessage: 'Ce champ est requis par le système pour la création de {entity}',
    fieldNameLabel: 'Nom du champ',
    deletefieldsHeading: 'Supprimer les champs',
    keepFieldsHeading: 'Conserver les champs',
    newText: 'Nouveau',
    fieldText: 'champ',
    warningMessageWithFieldNames: 'Vous êtes sur le point de supprimer le champ {fieldsArray} ',
    warningMessageMoreFields: 'et {number} autres champs. La suppression de ces champs supprimera également toutes les données qu\'ils contiennent déjà. ',
    warningMessageLessFields: 'champs. La suppression de ces champs supprimera également toutes les données qu\'ils contiennent déjà.',
    deletefieldsHeadingConfirmation: 'Supprimer les champs et leurs données',
    deleteSkillsButtonLabel: 'Supprimer les compétences',
    keepSelectedSkillsLabel: 'Conserver les compétences sélectionnées',
    fieldPropertiesLicenseWarning: 'Vous êtes proche de la limite autorisée par votre licence. {contactUs} pour augmenter la limite de votre plan.',
    fieldPropertieslicenseError: 'Vous avez atteint la limite autorisée par votre licence. {contactUs} pour augmenter la limite de votre plan.',
    showLookupLink: 'Afficher les valeurs de recherche',
    lookupValueUniqueValidation: 'Cette valeur a déjà été ajoutée',
    pressEnterMessage: 'Appuyez sur Entrée pour ajouter une valeur',
    defaultValueLabel: 'Valeur par défaut',
    noDefaultValueStaticHeaderText: 'Aucune valeur par défaut',
    multipleDefaultValueStaticHeaderText: 'Valeurs multiples',
    noDefaultPlaceHolder: 'Aucune valeur par défaut',
    invalidDefaultValueValidation: 'Valeur par défaut non valide',
    defaultValueSelectOwnValueText: 'Sélectionnez une valeur ou laissez le champ vide',
    defaultValueInheritValueText: 'Hériter la valeur de l\'utilisateur connecté',
    defaultValueInheritSummaryText: 'Hériter de l’utilisateur',
    defaultValueFieldText: 'Le champ {fieldName} de l’utilisateur sera hérité',
    defaultValueFieldDisabledText: 'Une nouvelle liste de valeurs de recherche sera disponible et utilisable comme liste par défaut une fois que vous aurez enregistré ce champ',
    inheritUserField: 'Champ « {fieldName} » de l’utilisateur',
    yesMsg: 'Oui',
    noMsg: 'Non',
    //Field look up values config
    fieldLookupValueAddButton: 'Ajouter une valeur',
    noResultsText: 'Aucun résultat',
    fieldLookupValuesDescription: 'Ajoutez, modifiez ou supprimez des valeurs affichées dans les listes. Par exemple, une liste Emplacement peut afficher des valeurs telles que Londres, Paris et New York.',
    fieldLookupValuesEntityData: 'Champs utilisant actuellement ces valeurs',
    saveFieldLookupValueAlert: 'Supprimer les valeurs sélectionnées ?',
    saveFieldLookupValueWarning: 'Si vous supprimez les valeurs, elles n’apparaîtront plus dans les listes déroulantes de recherche et dans toutes les autres sections où ces valeurs sont normalement affichées.',
    fieldLookupCheckMessage: 'Vous avez sélectionné {deleteCount} valeurs à supprimer.',
    yesDeleteValuesButtonLabel: 'Oui, supprimez les valeurs',
    noKeepValuesButtonLabel: 'Non, conservez les valeurs',
    optionIsRequiredMessage: 'L’option est requise',
    optionAlreadyExist: 'L’option existe déjà',
    uniqueValueNameMessage: 'Veuillez saisir un nom de valeur unique',
    requiredValueNameMessage: 'Veuillez indiquer un nom de valeur',
    fieldLookupValueInUseTitle: 'Les listes de valeurs n\'ont pas pu être supprimées',
    fieldLookupValueInUseMessage: 'Les listes de valeurs actuellement utilisées par un champ ne peuvent pas être supprimées.',
    fieldLookupValueInUseSubMessage: 'Vous devez supprimer tous les champs associés avant de supprimer les listes de valeurs.',
    fieldLookupValuesSortText: 'Ordre des valeurs',
    fieldLookupValuesDefaultSortModeText: 'Alphabétique (A-Z)',
    fieldLookupValuesCustomSortModeText: 'Incrémentiel (ordre personnalisé)',
    fieldLookupValuesSortModeDescriptionText: 'Les menus et les listes présentent ces valeurs dans cet ordre',
    fieldLookupValuesCustomSortGridText: 'Placer la valeur la moins significative en premier dans la liste',
    noneText: 'aucun',
    typeNewValue: 'Saisissez une nouvelle valeur',
    //System Settings
    fieldPropertiesPageHeader: 'Champs',
    fieldLookupValuesPageHeader: 'Valeurs',
    gridColoumnTitle_Option: 'Option',
    gridColoumnTitle_Target_Billability: 'Utilisation facturable',
    //PageNames
    pageNameHeader: 'Noms de pages',
    pageNamesHeaderSummary: 'Sélectionnez les pages avec leur nom d\'affichage pour le menu de gauche.',
    menuListTitle: 'Liste des menus',
    settingValMsg: 'Veuillez entrer la valeur de paramètre',
    maxSizeFieldMsg: 'La taille maximale du champ peut être de 40 caractères',
    displayText: 'Affichage',
    enableText: 'Activer',
    summaryPageNameText: 'Logo - aucune étiquette visible',
    //dynamic generated child sections from setting api (System settings)
    Talent_Profile: 'Profil de talent',
    Jobs: 'Missions',
    Scheduler: 'Planificateur',
    Timesheets: 'Relevé d\'heures',
    Report: 'Rapport',
    Role_inbox: 'Entrée poste',
    Roles_board: 'Tableau des postes',
    Table_View: 'Format tableau',
    //Currencies
    currenciesPageHeader: 'Devises',
    //charge codes
    chargeTypePageHeader: 'Types de charge',
    whiteSpaceValidationChargeType: 'Seuls les espaces blancs ne sont pas autorisés dans le type de frais',
    chargeCodeContent: 'Les types de frais vous permettent de créer différents tarifs selon le type de mission. Créez plusieurs types de frais pour couvrir les types de missions effectuées dans votre entreprise.',
    chargeRateContent: 'Ajoutez, modifiez ou supprimez des taux de coût et de revenu correspondant à des codes de facturation et des plages horaires spécifiques {ChargeRateLinkText}',
    chargeRateRedirection: 'ici.',
    chargeCodeUsageWarning: 'Ce type de frais est actuellement <strong>utilisé par {chargeTypeJobCount} missions.</strong>',
    chargeCodeWarning: 'Si vous supprimez ce type de frais, il sera supprimé de tous les enregistrements et ne pourra pas être utilisé dans le cadre des informations budgétaires et des rapports.',
    chargeTypeDeletionWarning: 'Supprimer le type de frais {chargeTypeName}',
    deleteChargeTypePrimaryButton: 'Supprimer le type de frais',
    deleteChargeTypeSecondaryButton: 'Conserver le type de frais',
    chargeCodeCheckMessage: 'Je comprends l\'impact de la suppression de ce type de frais',
    chargeCodeUniqueValidation: 'Veuillez saisir un type de frais unique',
    chargeCodeReqdValidation: 'Le type de frais est requis',
    chargeCodeAddButton: 'Ajouter un type de frais',
    noChargeCodeAvailable: 'Aucun type de frais disponible',
    waterMarkChargeCode: 'Type de frais sans titre',
    waterMarkDescription: 'Description du type de frais',
    chargeTypeHeader: 'Type de frais',

    //charge rates
    chargeRatePageHeader: 'Taux de charge',
    hourlyChargeRatesHeading: 'Tarifs horaires par type de mission',
    waterMarkRevenue: 'Revenu du taux de facturation',
    waterMarkCost: 'Coût du taux de facturation',
    chargeRateDeleteMessage: 'Vous allez supprimer ce taux de facturation et ainsi empêcher toute utilisation future.',
    chargeRateDeleteSubMessage: 'Ce taux de facturation sera conservé sur les réservations existantes.',
    cantUndoMessage: 'Vous ne pouvez pas annuler cette opération.',
    checkMessage: 'Je comprends les conséquences de cette suppression',
    deleteChargeRateTitle: 'Supprimer le taux de facturation « {deletedChargeRate} » ?',
    deleteChargeCodeSpecificChargeRateTitle: 'Supprimer le taux de facturation « {deletedChargeCodeName} » de « {deletedChargeRate} » ?',
    deleteChargeRateHeading: 'Ce tarif est actuellement utilisé par {chargeRateResourceCount} ressources.',
    deleteChargeRateWarningMessage: 'Si vous le supprimez, il sera supprimé de tous les enregistrements et ne pourra pas être utilisé dans le cadre des informations budgétaires et des rapports.',
    deleteChargeRateCheckboxMessage: 'Je comprends l\'impact de la suppression de ce taux de facturation',
    deleteChargeRateButtonLabel: 'Supprimer le taux de facturation',
    keepChargeRateButtonLabel: 'Conserver le taux de facturation',
    editChargeRateTitle: 'Modifier le taux de facturation « {editedChargeRateName} » actuel ?',
    editChargeRateHeading: 'Il s\'agit du taux de facturation actuel pour le type de frais « {editedChargeRateName} ».',
    editChargeRateSubHeading: 'Si vous modifiez ce taux de facturation, la modification affectera tous les calculs et rapports pour cette période.',
    editChargeRateWarningMessage: 'Voulez-vous vraiment modifier le taux de facturation actuel de « {editedChargeRateName} » ?',
    editChargeRatePrimaryButton: 'Modifier le taux actif',
    addCostRevenueButtonLabel: 'Ajouter des coûts et des revenus',
    customGridRangeOverlappingMessage: 'Il existe déjà un coût et un revenu pour les dates de cette plage',
    customGridRangeRequiredMessage: 'Veuillez indiquer une plage de dates',
    customGridRevenueRequiredMessage: 'Veuillez indiquer le revenu',
    customGridCostRequiredMessage: 'Veuillez indiquer le coût',
    chargeRateNameField: 'Nom du taux de facturation',
    chargeRateTitleRequiredMessage: 'Veuillez indiquer le nom du taux de facturation',
    chargeRateTitleUniqueMessage: 'Veuillez saisir un nom de taux de facturation unique',
    customGridNegativeMessage: 'Cette valeur ne peut pas être négative',
    dateRangeLabel: 'Plage de dates',
    revenueLabel: 'Revenu',
    costLabel: 'Coût',
    //skills
    noRecommendations: 'Aucune nouvelle recommandation',
    addSkillsHeading: 'Ajouter des compétences pour ce type de compétence',
    activeSkillTitle: '« {activeSkillConfiguration} » est l’une des sections de compétences qui apparaissent sur le profil de talent d’un utilisateur.',
    skillHeaderText: 'Compétences sous {activeSkillConfiguration}',
    skillHeaderDescription: 'Les utilisateurs pourront choisir les compétences suivantes dans la section « {activeSkillConfiguration} » de leur profil.',
    skillImportMessage: 'Pour les mises à jour groupées, la fonction {Import} vous semblera sans doute plus pratique',
    addSkillButtonFromCommandBar: 'Ajouter une compétence',
    skillNameLabel: 'Nom de la compétence',
    skillType: 'Taper',
    addSkillCategoryText: 'Ajouter une catégorie',
    skillCategory: 'Catégorie',
    skillSubCategory: 'Sous-catégorie',
    tagsLabel: 'Mots-clés',
    skillTagExplaination: 'Les étiquettes peuvent être jointes aux compétences. Les étiquettes facilitent la recherche de compétences, en particulier lorsqu\'elles sont identifiées différemment. Appuyer sur entrée après avoir ajouté chaque étiquette avant de sauvegarder.',
    skillNameRequired: 'Veuillez saisir le nom de la compétence',
    uniqueSkillNameRequired: 'Veuillez saisir un nom de compétence unique',
    uniqueSkillItem: 'Le nom de la compétence doit être unique',
    deleteSkillFieldsPopupHeading: 'Supprimer les champs sélectionnés ?',
    deleteSkillFieldsPopupWarningMessage: 'Souhaitez-vous supprimer définitivement les champs de compétence sélectionnés ? {number} champs de compétence seront supprimés des types de compétence et des profils de ressources, ainsi que de toutes les autres zones où ils figurent normalement.',
    deleteSkillFieldsButtonLabel: 'Supprimer les champs de compétence',
    keepSkillFieldsButtonLabel: 'Conserver les champs de compétences',
    deleteSkillLevelsPopupHeading: 'Supprimer les niveaux sélectionnés ?',
    deleteSkillLevelsPopupWarningMessage: 'Souhaitez-vous supprimer définitivement les niveaux de compétence sélectionnés ? Les niveaux de compétence supprimés seront supprimés des profils de ressources et de toutes les autres zones où ils figurent normalement.',
    deleteSkillLevelsButtonLabel: 'Supprimer les niveaux de compétence',
    keepSkillLevelsButtonLabel: 'Conserver les niveaux de compétences',
    deleteSkillsPopupHeading: 'Supprimer les compétences sélectionnées ?',
    deleteSkillsPopupWarningMessage: 'Souhaitez-vous supprimer définitivement les compétences sélectionnées ? {number} compétences seront supprimées du système et des profils des utilisateurs, ainsi que de toutes les autres zones où elles figurent normalement.',
    keepSkillsButtonLabel: 'Conserver les compétences',
    deleteSkillsAndLevelsPopupHeading: 'Supprimer les compétences et les niveaux de compétence sélectionnés ?',
    deleteSkillsAndLevelsPopupWarningMessage: 'Souhaitez-vous supprimer définitivement les compétences et niveaux de compétence sélectionnés ?',
    deleteSkillsAndFieldsPopupHeading: 'Supprimer les compétences et champs de compétences sélectionnés ?',
    deleteSkillsAndFieldsPopupWarningMessage: 'Souhaitez-vous supprimer définitivement les compétences et champs de compétence sélectionnés ?',
    deleteLevelsAndFieldsPopupHeading: 'Supprimer les niveaux de compétence et les champs de compétence sélectionnés ?',
    deleteLevelsAndFieldsPopupWarningMessage: 'Souhaitez-vous supprimer définitivement les niveaux de compétence et champs de compétence sélectionnés ?',
    deleteSkillsLevelsAndFieldsPopupHeading: 'Supprimer les compétences, les niveaux de compétence et les champs de compétence sélectionnés ?',
    deleteSkillsLevelsAndFieldsPopupWarningMessage: 'Souhaitez-vous supprimer définitivement les compétences, niveaux de compétence et champs de compétence sélectionnés ?',
    deletePopupWarningMessage: 'Les éléments seront supprimés de ce type de compétence et retirés des profils de ressources et de toute autre zone où ils figurent normalement.',
    deleteSkillsPopupConfirmation: 'Je comprends les conséquences de cette action.',
    deleteItemsButtonLabel: 'Supprimer ces éléments',
    keepItemsButtonLabel: 'Conserver ces éléments',
    deleteAndModifySkillFieldsPopupHeading: 'Enregistrer les modifications et supprimer les champs sélectionnés ?',
    deleteAndModifySkillFieldsPopupWarningMessage: 'Souhaitez-vous enregistrer les modifications et supprimer définitivement les champs de compétence sélectionnés ? {number} champs de compétence seront supprimés des types de compétence et des profils de ressources, ainsi que de toutes les autres zones où ils figurent normalement.',
    deleteAndModifySkillLevelsPopupHeading: 'Enregistrer les modifications et supprimer les niveaux sélectionnés ?',
    deleteAndModifySkillLevelsPopupWarningMessage: 'Souhaitez-vous enregistrer les modifications et supprimer définitivement les niveaux de compétence sélectionnés ? Les niveaux de compétence supprimés seront supprimés des profils de ressources et de toutes les autres zones où ils figurent normalement.',
    deleteAndModifySkillsPopupHeading: 'Enregistrer les modifications et supprimer les compétences sélectionnées ?',
    deleteAndModifySkillsPopupWarningMessage: 'Souhaitez-vous enregistrer les modifications et supprimer définitivement les compétences sélectionnées ? {number} compétences seront supprimées du système et des profils des utilisateurs, ainsi que de toutes les autres zones où elles figurent normalement.',
    deleteAndModifySkillsAndLevelsPopupHeading: 'Enregistrer les modifications et supprimer les compétences et les niveaux de compétence sélectionnés ?',
    deleteAndModifySkillsAndLevelsPopupWarningMessage: 'Souhaitez-vous enregistrer les modifications et supprimer définitivement les compétences et niveaux de compétence sélectionnés ?',
    deleteAndModifySkillsAndFieldsPopupHeading: 'Enregistrer les modifications et supprimer les compétences et les champs de compétence sélectionnés ?',
    deleteAndModifySkillsAndFieldsPopupWarningMessage: 'Souhaitez-vous enregistrer les modifications et supprimer définitivement les compétences et champs de compétences sélectionnés ?',
    deleteAndModifyLevelsAndFieldsPopupHeading: 'Enregistrer les modifications et supprimer les niveaux de compétence et les champs de compétence sélectionnés ?',
    deleteAndModifyLevelsAndFieldsPopupWarningMessage: 'Souhaitez-vous enregistrer les modifications et supprimer définitivement les niveaux et champs de compétence sélectionnés ?',
    deleteAndModifySkillsLevelsAndFieldsPopupHeading: 'Enregistrer les modifications et supprimer les compétences, niveaux de compétence et champs de compétence sélectionnés ?',
    deleteAndModifySkillsLevelsAndFieldsPopupWarningMessage: 'Souhaitez-vous enregistrer les modifications et supprimer définitivement les compétences, niveaux de compétence et champs de compétence sélectionnés ?',
    skillCategoryDeletePopupHeading: 'Supprimer le type de compétence ?',
    skillCategoryDeletePopupWarningMessage: 'Souhaitez-vous supprimer cette catégorie de compétences ? Cette catégorie de compétences contient {number} compétences, qui seront toutes dissociées. Les niveaux ainsi que les champs associés à cette catégorie seront supprimés du système, ainsi que tous les profils qui y font référence.',
    deleteSkillCategoryButtonLabel: 'Supprimer le type de compétence',
    keepSkillCategory: 'Conserver le type de compétence',
    fieldAlreadyExistHeader: 'Veuillez saisir un nom de champ unique',
    fieldAlreadyExistDescription: 'Ce nom de champ existe déjà. Veuillez utiliser le champ existant ou renommer ce champ pour qu\'il soit unique.',
    skillNamePlaceholder: 'Saisissez le nom de la compétence',
    skillDescriptionPlaceholder: 'Saisissez la description de la compétence',
    skillNameHeader: 'Nom de compétence',
    fieldTypeHeader: 'Type de champ',
    categoryText: 'Catégorie',
    defaultValueText: 'Valeur par défaut',
    newTag: 'Nouveau mot-clé',
    headerLevels: 'Niveaux',
    licenseCountSkill: '{activeCount} compétences utilisées sur {licensedCount} pour ce type de compétence',
    skillsLicenseWarning: 'Vous approchez de la limite autorisée par votre licence en matière de compétences. {contactUs} pour augmenter la limite de votre plan.',
    skillsLicenseError: 'Vous avez atteint la limite autorisée par votre licence en matière de compétences. {contactUs} pour augmenter la limite de votre plan.',
    licenseCountSkillLevel: '{activeCount} niveaux utilisés sur {licensedCount} pour ce type de compétence',
    skillLevelsLicenseWarning: 'Vous êtes proche de la limite de niveaux autorisés par votre licence pour ce type de compétence. {contactUs} pour augmenter la limite de votre plan.',
    skillLevelsLicenseError: 'Vous avez atteint la limite de niveaux autorisés par votre licence pour ce type de compétence. {contactUs} pour augmenter la limite de votre plan.',
    licenseCountSkillField: '{activeCount} champs utilisés sur {licensedCount} pour ce type de compétence',
    skillFieldsLicenseWarning: 'Vous êtes proche de la limite de champs autorisés par votre licence pour ce type de compétence. {contactUs} pour augmenter la limite de votre plan.',
    skillFieldsLicenseError: 'Vous avez atteint la limite de champs autorisés par votre licence pour ce type de compétence. {contactUs} pour augmenter la limite de votre plan.',
    adminCommandBarActionLabel: 'Actions',
    adminCommandBarEditLabel: 'Modifier',
    adminCommandBarUserStatusLabel: 'Utilisateur actif',
    adminCommandBarSetActiveLabel: 'Passer sur actif',
    adminCommandBarSetInactiveLabel: 'Passer sur inactif',
    adminCommandBarResendEmailInviteLabel: 'Envoyer un e-mail d’invitation',
    adminCommandBarResetPassphraseLabel: 'Réinitialiser le mot de passe',
    disabledSkillType: 'Veuillez enregistrer ou annuler les modifications avant de continuer',
    adminCommandBarSendCMeSurveyLabel: 'Envoyer l\'enquête C-me',
    // Skill Category
    skillCategoryRequired: 'Veuillez saisir le nom de la catégorie',
    uniqueCategoryNameRequired: 'Veuillez saisir un nom de catégorie unique',
    primarySkillLabel: 'Compétence principale',
    secondarySkillLabel: 'Compétence secondaire',
    preferredSkillLabel: 'Compétence préférée',
    skillCategoryLabel: 'Catégories de compétences',
    importInProgressText: 'L\'importation des compétences depuis la bibliothèque Retain est en cours',
    subscribed: 'Abonné',
    notSubscribed: 'Non abonné',
    selectedImportDataLabel: '<bold>${skillCount} compétences choisies parmi ${categoryCount} catégories</bold>',
    skillCategoryExpiryMandatoryInfo: 'Sélectionnez Oui pour rendre le champ Expiration des compétences obligatoire',
    skillExpiryEnabledInfo: 'FR_Set this to Yes to enable Skill expiry fields for this skill category_FR',
    importClickInfo: 'Cliquer sur <bold>Importer</bold> mettra cette tâche d\'importation en file d\'attente',
    skillCategorySubscribeInfo: 'Abonnez-vous pour être notifié des mises à jour de cette catégorie de compétences',
    importProgressInfo: 'Veuillez consulter le <bold>Journal des opérations</bold> pour voir l’avancement de cette tâche',
    skillExpiryDateLabel: 'FR_Skill expiry is mandatory_FR',
    skillExpiryEnabledLabel: 'FR_Enable skill expiry dates_FR',
    preNotificationLabel: 'Notification avant expiration',
    watcherLabel: 'Observateur de la catégorie de compétences',
    existingSkillsHeader: 'Les compétences déjà ajoutées ou portant le même nom que des compétences existantes dans votre entreprise sont masquées dans cette liste',

    //skill Library
    addSkillLibrary: 'Ajouter depuis la bibliothèque Retain',
    skillLibraryHeader: 'Bibliothèque de compétences Retain',
    importLibraryStep1: {
        title: 'Choisir des compétences',
        description: 'Choisissez les compétences que vous souhaitez importer'
    },
    importLibraryStep2: {
        title: 'Vérifier',
        description: 'Vérifiez les compétences que vous avez sélectionnées'
    },
    importLibraryStep3: {
        title: 'Importer',
        description: 'Prêt à importer'
    },
    searchSkillPlaceholder:'FR_Search skill_FR',
    //Notifications
    notificationsPage: {
        commandBarConfig: {
            pageTitle: 'Notifications'
        },
        notificationSettingSummary: {
            notificationSettingSubHeading: 'Choisissez si vous souhaitez afficher les notifications par type.'
        },
        notificationSettingConfig: {
            onLabel: 'Activé',
            offLabel: 'Désactivé'
        },
        notificationEvents: {
            bookingAssignedLabel: 'Vous avez été affecté(e) à la mission ${jobName} du ${startDate} à ${endDate} ${timeAllocation} par ${editorName}',
            bookingUpdateLabel: 'Votre réservation pour la mission ${jobName}, du ${startDate} au ${endDate} ${timeAllocation}, a été modifiée par ${editorName}',
            bookingDeleteLabel: 'Vous n’êtes plus affecté(e) à la mission ${jobName} du ${startDate} à ${endDate} ${timeAllocation}',
            roleRequestCreateLabel: 'La ressource ${resourceName} a été demandée pour la mission ${jobName}, du ${startDate} à ${endDate} ${timeAllocation}, par ${editorName}',
            roleRequestRejectLabel: 'Votre demande de ${resourceName} pour la mission ${jobName}, du ${startDate} à ${endDate} ${timeAllocation}, a été rejetée par ${editorName}',
            roleRequestLiveLabel: 'Votre demande de ${resourceName} pour la mission ${jobName}, du ${startDate} à ${endDate} ${timeAllocation}, a été activée par ${editorName}',
            repeatBookingAssignedLabel: 'Vous avez été affecté(e) à la mission ${jobName} du ${startDate} à ${endDate} ${timeAllocation} par ${editorName}. Cette réservation se répète tous les ${interval} jusqu’au ${untilDate}',
            repeatBookingUpdateLabel: 'Votre réservation pour la mission ${jobName}, du ${startDate} au ${endDate} ${timeAllocation}, a été modifiée par ${editorName}. Cette réservation se répète tous les ${interval} jusqu’au ${untilDate}',
            repeatBookingDeleteLabel: 'Vous n’êtes plus affecté(e) à la mission ${jobName} du ${startDate} à ${endDate} ${timeAllocation}. Cette réservation s’est répétée tous les ${interval} jusqu’au ${untilDate}',
            loadingSectionSuffix: '% d\'heures de travail',
            timeSectionSuffix: 'heures réservées au total',
            hoursPerDaySuffix: 'heures par jour',
            resourceSkillExpiryLabel: 'FR_You have skills set to expire in ${expiryDay} days ⏳ Renew it before the expiry date to keep your credentials up to date_FR',
            resourceManagerSkillExpiryLabel: 'FR_${resourceName} has skills set to expire in ${expiryDay} days ⏳ Ensure they renew it before the expiry date to keep your credentials up to date_FR',
            resourceSkillRecommendationLabel: 'FR_Update your skills and get noticed! Here are some skills you could add to your profile_FR',
            skillExpiryLabel: 'FR_${skillName} ${entityType} expires on ${expiryDate}_FR',
            extraSkillExpiryLabel: 'FR_${count} more expiring soon_FR'
        },
        notificationActions: {
            viewBookingLabel: 'Afficher ${bookingSingularLowerAlias}',
            editBookingLabel: 'Modifier ${bookingSingularLowerAlias}',
            viewPlansLabel: 'Voir les plans',
            viewRoleGroupLabel: 'Afficher dans ${entitySingularLower}',
            makeLiveLabel: 'Mise en ligne',
            markAsReadLabel: 'Marquer comme lu',
            markAsUnreadLabel: 'Marquer comme non lu',
            deleteLabel: 'Supprimer'
        },
        notificationHistoryPageHeader: {
            informationMessage: 'Afficher toutes les notifications. Les notifications de lecture sont automatiquement supprimées au bout de 30 jours',
            markAllReadLabel: 'Tout marquer comme lu',
            loadMoreButtonLabel: 'Afficher plus'
        },
        notificationTabs: {
            notificationHistoryLabel: 'Historique',
            notificationSettingsLabel: 'Paramètres'
        },
        notificationsSettings: {
            pageTitle: 'Notifications',
            notificationSettingsHeader: 'Notifications par défaut',
            notificationsSettingsAdminPageDescription: 'Réglez les paramètres de notification par défaut pour le nouvel utilisateurs. Les utilisateurs peuvent modifier ces paramètres en fonction de leurs besoins dans Notifications > Settings [Notifications > Paramètres]',
            notificationsSettingsDescription: 'Choisissez ce dont vous voulez être notifié(e) et comment',
            bookingNotifications: '${bookingSingularAlias} notifications',
            allNotifications: 'Toutes les notifications',
            bookingPost: 'Lorsqu\'on vous attribue un(e) ${bookingSingularAlias}',
            bookingPatch: 'Lorsqu\'un(e) ${bookingSingularAlias} qui vous est attribué(e) est mis(e) à jour',
            bookingDelete: 'Lorsque un(e) ${bookingSingularAlias} vous est désattribué(e) ou est supprimé(e)',
            emailFrequency: 'Fréquence des e-mails',
            roleNotificaitons: '${rolerequestSingularAlias} notifications',
            roleRequestCreate: 'Lorsqu\'un(e) ${resourceSingularAlias} que vous gérez est demandé(e)',
            roleRequestReject: 'Lorsqu\'un(e) ${rolerequestSingularAlias} que vous avez créé(e) est rejeté(e)',
            roleRequestLive: 'Lorsqu\'un(e) ${rolerequestSingularAlias} que vous avez créé(e) est activé(e)',
            webApp: 'Application web',
            email: 'E-mail',
            globalNotificationSettingsHeaderTitle: 'Paramètres de notification globaux',
            globalNotificationSettingsHeaderDescription: 'Gérer les paramètres de notification pour tous les utilisateurs',
            globalNotificationSettingsToggleTitle: 'Notifier les utilisateurs des \n\nréservations non confirmées',
            globalNotificationSettingsToggleDescription: 'Inclure les réservations non confirmées dans les notifications de réservation',
            resourceSkillNotifications: 'FR_Skill notifications_FR',
            resourceSkillExpiry: 'FR_When a skill you have is expiring_FR',
            resourceManagerSkillExpiry: 'FR_When a resource you manage has a skill that\'s expiring_FR',
            resourceSkillRecommendation: 'FR_Recommend skills to add to your profile based on your job title or primary skill_FR'
        }
    },

    //Timesheets Page
    timesheetsPage: {
        noTimesheetsAvailable: 'Vous n’avez aucune heure enregistrée',
        jobLabel: 'Mission',
        monday: 'Lundi',
        tuesday: 'Mardi',
        wednesday: 'Mercredi',
        thursday: 'Jeudi',
        friday: 'Vendredi',
        saturday: 'Samedi',
        sunday: 'Dimanche',
        total: 'Total',
        hours_logged: 'heures enregistrées',
        commandBarConfig: {
            pageTitle: 'Feuilles de temps'
        },
        hoursRequiredLabel: 'Ce champ est obligatoire',
        maximumHourErrorLabel: '24 maximum',
        minimumHourErrorLabel: '0 minimum',
        showTimesheetsLabel: 'Afficher la feuille de temps issue de',
        submitButtonLabel: 'Enregistrer',
        cancelButtonLabel: 'Annuler',
        timesheetDeletionWarning: 'Retirer la mission ?',
        timesheetDataDeletionMessage: 'Voulez-vous retirer <bold>${jobDescription}</bold> et les heures associées de cette feuille de temps ?',
        timesheetCheckMessage: 'Je comprends l\'impact de la suppression de ces données de feuille de temps',
        deleteTimesheetPrimaryButton: 'Oui, supprimer la mission',
        deleteTimesheetSecondaryButton: 'Non, conserver la mission',
        addJobButtonLabel: 'Ajouter une mission',
        applyJobButtonLabel: 'Ajouter',
        hoursAriaLabel: 'Entrer les heures'
    },

    //Reporting settings Page
    reportingTitle: 'Rapport',
    reportSettingPage: {
        datasetRefreshModalTitle: 'FR_Dataset refresh status_FR',
        lastRunLabel: 'FR_Last Run_FR',
        successStatus: 'FR_Dataset refresh completed successfully_FR',
        failureStatus: 'FR_Dataset refresh failed_FR',
        inProgressStatus: 'FR_Dataset refresh is in progress..._FR',
        cancelledStatus: 'FR_Dataset refresh was cancelled_FR',
        unknownStatus: 'FR_Unknown status_FR',
        datasetRefreshButton: 'FR_Refresh dataset_FR',
        cancelButton: 'FR_Cancel_FR'
    },

    //Report Page
    reportPage: {
        commandBarConfig: {
            pageTitle: 'Rapports'
        },
        pageTitle: 'Rapports',
        editLabel: 'Modifier',
        viewLabel: 'Afficher',
        printLabel: 'Imprimer',
        reportsIHaveCreatedLabel: 'Rapports que j’ai créés',
        sharedReportsItemLabel: 'Rapports qui m\'ont été communiqués',
        manageMyReportsItemLabel: 'Gérer mes rapports...',
        reportNoEditPermissions: 'Autorisation insuffisante pour modifier ce rapport',
        emptyReportTitle: 'Aucun rapport trouvé',
        emptyReportPageText: 'Il n\'y a aucun rapport que vous pouvez consulter',
        sharedReportsSectionTitle: 'Rapports partagés que je peux modifier',
        reportDetailsTitle: 'Détails du rapport',
        systemInfoTitle: 'Informations système',
        updatedByLabel: 'Mis à jour par',
        createdByLabel: 'Créé par',
        createdOnLabel: 'Créé le',
        updatedOnLabel: 'Mis à jour le',
        nameLabel: 'Nom',
        reportAccessLabel: 'Accès au rapport',
        emptyReportDetailsText: 'Commencer à créer des rapports pour les voir apparaître dans cette fenêtre modale.',
        modalHeaderTitle: 'Gérer mes rapports',
        newReportButtonLabel: 'Nouveau rapport',
        newReportButtonTooltip: 'Maximum de ${licenseCount} rapports autorisés par titulaire. Vous ne pourrez pas voir tous les rapports sans accès en lecture.',
        deleteButtonLabel: 'Supprimer le rapport sélectionné',
        saveChangesButtonLabel: 'Enregistrer les modifications',
        discardButtonLabel: 'Annuler les modifications',
        cancelButtonLabel: 'Annuler',
        pendingDeleteButtonLabel: 'Suppression en cours...',
        pendingSaveButtonLabel: 'Enregistrement en cours...',
        unsavedChangesLabel: 'Modifications non enregistrées',
        youHaveUnsavedChangesLine: 'Vous avez des modifications non enregistrées sur ce rapport. Voulez-vous tout de même quitter ?',
        allProfilesLabel: 'Tous les profils',
        readOnlyLabel: 'Lecture seule',
        editAccessLabel: 'Modifier l\'accès',
        helpTextLabel: 'Les créateurs peuvent toujours consulter et modifier leurs rapports',
        maxLimitReachedLabel: 'Maximum de 20 ${resourcePluralLowerAlias} ou profils de sécurité',
        reportAccessEntityPicker: {
            searchLabel: 'Rechercher',
            applyButtonText: 'Appliquer',
            notLoadedLabel: 'Non chargé',
            notLoadedValueLabel: 'Valeur non chargée',
            showMoreButtonText: 'Montrer plus'
        },
        deleteReportPrompt: {
            title: 'Supprimer le rapport « ${reportName} » ?',
            confirmDeleteLabel: 'Oui, supprimer le rapport',
            declineDeleteLabel: 'Non, conserver le rapport',
            createdByLine: 'Ce rapport a été créé par ${createdBy}.',
            permanentlyDeleteLine: 'Souhaitez-vous supprimer définitivement ce rapport ?',
            youLabel: 'vous'
        },
        toasterMessages: {
            create: 'Rapport créé',
            delete: 'Rapport supprimé',
            edit: 'Rapport modifié',
            save: 'Rapport enregistré'
        },
        selectLabel: 'Sélectionner',
        resourcesLabel: '${resourcePluralCapitalAlias}',
        securityProfilesLabel: 'Profils de sécurité',
        emptyNameError: 'Veuillez entrer un nom',
        duplicateNameError: 'Ce nom est déjà utilisé. Veuillez entrer un nom unique.'
    },

    //Callback component
    callbackComponentText: 'Redirection en cours...',

    // Planner/Jobs page
    plannerPage: {
        commandBarConfig: {
            scenarioLabel: '${rolerequestgroupSingularCapitalAlias}',
            pageTitle: 'Planificateur',
            addLabel: 'Ajouter',
            editLabel: 'Modifier',
            editRoleByNameButtonLabel: 'Modifier ${rolerequestPluralCapitalAlias} par nom',
            editRoleByCriteriaButtonLabel: 'Modifier ${rolerequestPluralCapitalAlias} par critère',
            viewLabel: 'Afficher',
            barsLabel: 'Options de la barre',
            showLabel: 'Afficher',
            increaseDateRangeLabel: 'Augmenter la plage de dates',
            decreaseDateRangeLabel: 'Réduire la plage de dates',
            dateRangeLabel: 'Plage de dates',
            goToTodayLabel: 'Aller à aujourd\'hui',
            goToDateLabel: 'Aller à la date',
            jobsLabel: 'Missions',
            resourcesLabel: 'Ressources',
            filtersLabel: 'Filtres',
            bookingLabel: 'Réservation',
            roleLabel: 'Rôle',
            jobLabel: 'Mission',
            rolefromTemplateLabel: '${rolerequestSingularCapitalAlias} à partir d\'un modèle',
            clientLabel: 'Client',
            cutLabel: 'Couper',
            copyLabel: 'Copier',
            pasteLabel: 'Coller',
            restartLabel: 'Redémarrer',
            archiveLabel: 'Archiver',
            rejectLabel: 'Rejeter',
            makeLiveLabel: 'Mise en ligne',
            submitRequestLabel: 'Envoyer une demande',
            createLabel: 'Créer',
            deleteLabel: 'Supprimer',
            manageLabel: 'Gérer',
            manageRoleTemplatesLabel: 'Gérer les modèles ${rolerequestSingularLowerAlias}',
            moreLabel: 'Plus',
            newWorkspaceLabel: 'FR_New Workspace_FR',
            saveAsNewWorkspaceLabel: 'FR_Save as a new workspace_FR',
            manageMyWorkspacesLabel: 'FR_Manage My Workspaces_FR',
            privateWorkspacesLabel: 'FR_Private Workspaces_FR',
            publicWorkspacesLabel: 'FR_Public Workspaces_FR',
            noPublicWorkspacesCreatedLabel: 'FR_No public workspaces have been created_FR',
            noPrivateWorkspacesCreatedLabel: 'FR_No private workspaces have been created_FR',
            newPlanLabel: 'Nouveau plan',
            saveAsNewPlanLabel: 'Sauvegarder en tant que nouveau plan',
            manageMyPlansLabel: 'Gérer mes plans',
            privatePlansLabel: 'Plans privés',
            publicPlansLabel: 'Plans publics',
            saveChangesLabel: 'Enregistrer les modifications',
            saveChangesToPublicLabel: 'Enregistrer les modifications dans la version publique',
            noPublicPlansCreatedLabel: 'Aucun plan public n’a été créé',
            noPrivatePlansCreatedLabel: 'Aucun plan privé n’a été créé',
            noRoleTemplatesCreatedLabel: 'Aucun modèle n\'a été ajouté',
            customDateRangeLabel: 'Plage de dates personnalisée',
            dayLabel: 'Jour',
            '5daysLabel': '5 jours',
            '7daysLabel': '7 jours',
            '10daysLabel': '10 jours',
            weekLabel: 'Semaine',
            '2weeksLabel': '2 semaines',
            '4weeksLabel': '4 semaines',
            '6weeksLabel': '6 semaines',
            monthLabel: 'Mois',
            '2monthsLabel': '2 mois',
            '3monthsLabel': '3 mois',
            '6monthsLabel': '6 mois',
            yearLabel: 'Année',
            weekendsLabel: 'Week-ends',
            potentialConflicts: 'Afficher les conflits potentiels',
            baseFilterLabel: 'Afficher les missions',
            rollForwardLabel: 'Dupliquer',
            rollForwardTooltipText: 'Copier l’entité ${bookingEntityAlias} sélectionnée vers une autre ${jobEntityAlias} ou date',
            byNameSuffix: 'par nom',
            byRequirementSuffix: 'par exigences',
            findResourcesLabel: 'Trouver ${resourceEntityAlias}...',
            findResourceToolTipText: 'Trouver ${resourceEntityAlias} sur la base de critères (f)',
            showMenuTooltipText: '${rolePluralUpperCase} non attribuées sont maintenant masqués par défaut. Utilisez le menu « Montrer » pour modifier cela.',
            showInViewLabel: 'Montrer la vue ${pluralViewNameAlias}',
            restorePlansLabel: 'Réinitialiser l\'affichage',
            restorePlanTooltipText: 'Rétablit l\'état initial de l\'affichage actuel. Utilisez le menu déroulant sur la droite pour sauvegarder cette vue comme plan.'
        },
        hideShowBarOptions: {
            hideHistoricEntityLabel: '${subRowEntityAlias} passés',
            hideFutureEntityLabel: '${subRowEntityAlias} futurs',
            hideUnassignedRowsEntityLabel: 'Rangées non attribuées',
            hideUnassignedBookingsEntityLabel: 'FR_Unassigned ${bookingPluralLowerCase}_FR',
            hideRolesLabel: '${rolePluralCapitalized}',
            hideInactiveEntityLabel: 'Inactif ${resourcePluralCapitalized}',
            hidePastEntitiesExplanation: 'Afficher les ${subRowEntityAlias} pour lesquels il n\'y a que des ${bookingPluralLowerCase} qui se terminent aujourd’hui ou avant',
            hideFutureEntitiesExplanation: 'Afficher les ${subRowEntityAlias} pour lesquels il n\'y a que des ${bookingPluralLowerCase} qui commencent après la fin de la plage de dates visible',
            hideRolesExplanation: 'Afficher les ${subRowEntityAlias} pour lesquels il y a des ${rolePluralLowerCase}',
            hideDraftRolesExplanation: 'Afficher les brouillons de ${roleSingularCapitalized}',
            hideRequestedRolesExplanation: 'Afficher les demandes de ${rolePluralCapitalized} qui pourraient devenir des ${bookingPluralLowerCase} actives',
            toggleShowUnassignedRoles: '${rolePluralUpperCase} non attribuées',
            toggleShowRolesByName: '${rolePluralCapitalized} par nom',
            toggleShowRolesByRequirements: '${rolePluralCapitalized} par critère',
            hideJobTimelineToggleLabel: '${jobEntityAlias} chronologie',
            hideJobMilestonesToggleLabel: '${jobEntityAlias} jalons',
            hideJobTimelineExplanation: 'Afficher les dates de début et de fin de ${jobEntityAlias}',
            hideJobMilestonesExplanation: 'Afficher les dates spécifiques de ${jobEntityAlias}'
        },
        selectionBar: {
            editAllButtonLabel: 'Modifier',
            deleteAllButtonLabel: 'Supprimer',
            editButtonLabel: 'Modifier',
            editRoleByNameButtonLabel: 'Modifier ${rolerequestPluralCapitalAlias} par nom',
            editRoleByCriteriaButtonLabel: 'Modifier ${rolerequestPluralCapitalAlias} par critère',
            deleteButtonLabel: 'Supprimer',
            archiveAllButtonLabel: 'Archiver',
            archiveButtonLabel: 'Archiver',
            restartAllButtonLabel: 'Redémarrer',
            restartButtonLabel: 'Redémarrer',
            submitRequestAllButtonLabel: 'Envoyer une demande',
            submitRequestButtonLabel: 'Envoyer une demande',
            createButtonLabel: 'Créer',
            makeLiveSingularButtonLabel: 'Mise en ligne',
            makeLivePluralButtonLabel: 'Tout mettre en ligne',
            insufficientRightsToEditAndDelete: 'Autorisation insuffisante pour modifier/supprimer ces ${entityAlias}',
            insufficientActionRights: 'Autorisations insuffisantes pour appliquer cette action à tous les ${entityAlias}',
            selectedLabel: 'sélectionnés',
            maxBookingsSuffix: 'max',
            rollForwardLabel: 'Dupliquer',
            rollForwardTooltipText: 'Copier l’entité ${bookingEntityAlias} sélectionnée vers une autre ${jobEntityAlias} ou date',
            showInView: 'Montrer dans la vue ${pluralViewNameAlias}'
        },
        barOptions: {
            defaultLabel: 'Par défaut',
            mediumLabel: 'Moyenne',
            expandedLabel: 'Étendue'
        },
        showLabelsOnBarsLabel: 'Afficher les libellés sur les barres',
        legendLabel: 'Légende',
        barFieldsLabel: 'Champs de barre ${barSingularAlias}',
        colourSchemeLabel: 'Thème de couleur',
        customColourThemeLabel: 'Thème de couleur personnalisé',
        customColourSchemeLabel: 'Thème de couleur personnalisé ${barSingularAlias}',
        editedSuffix: 'modifié',
        createdSuffix: 'créé',
        deletedSuffix: 'supprimé',
        archivedSuffix: 'archivé',
        restartedSuffix: 'redémarré',
        rejectedSuffix: 'rejeté',
        requestedSuffix: 'demandé',
        liveSuffix: 'passé à',
        publishedRoleSuffix: 'publié dans ${marketplaceAlias}',
        scheduleRoleForPublishingSuffix: 'programmé pour publication dans ${marketplaceAlias}',
        publicationEditedSuffix: 'publication modifiée',
        publicationRemovedSuffix: 'supprimé de ${marketplaceAlias}',
        applyButtonText: 'Appliquer',
        searchLabel: 'Chercher',
        notLoadedLabel: 'non chargé',
        notLoadedValueLabel: 'valeur non chargée',
        goToPageLabel: 'Aller à la page',
        legend: {
            legendTitle: 'Légende',
            coloursColumnSubTitle: 'Les couleurs des barres sont basées sur les',
            barTypes: {
                draftRoles: 'Brouillons de ${rolerequestSingularCapitalAlias}',
                roleRequestsToLiveBookings: 'Demandes de ${rolerequestSingularCapitalAlias} qui pourraient devenir des ${bookingPluralLowerAlias} actives',
                unconfirmed: 'Non confirmé',
                planned: 'Planifié',
                excludesNonWorkingDays: 'Exclut les jours non travaillés',
                includesNonWorkingDays: 'Inclut les jours non travaillés',
                inConflict: 'En conflit',
                startDateNonWorking: 'La date de début tombe dans un week-end masqué',
                endDateNonWorking: 'La date de fin tombe dans un week-end masqué',
                bothDatesNonWorking: 'Les dates de début et de fin tombent dans un week-end masqué',
                roleRequestsOnLiveBookings: '${rolerequestSingularCapitalAlias} les demandes qui sont devenues actives ${bookingPluralLowerAlias}'
            },
            tabsTitlesMap: {
                colourTypesTabTitle: 'Thème de couleur',
                barTypesTabTitle: 'Types de barres',
                milestonesTabTitle: '${jobSingularAlias} détails'
            },
            jobDetailsLabels: {
                milestonesColumnTitle: 'Jalons',
                timelineColumnTitle: 'chronologie',
                linesColumnTitle: 'Lignes',
                normalLineLabel: '${jobSingularAliasLinesSection} avec les dates de début et de fin',
                dashedLineLabel: '${jobSingularAliasLinesSection} avec une date de début et/ou de fin manquante',
                lineEndsColumnTitle: 'Fin de ligne',
                onscreenDatesLabel: 'Date de début/fin ${jobSingularAliasLineEndsSection} à l\'écran',
                offscreenDatesLabel: 'Dates de début/fin ${jobSingularAliasLineEndsSection} hors écran',
                statesColumnTitle: 'Indique',
                incompletedStateLabel: 'Inachevé',
                completedStateLabel: 'Achevé',
                overduedStateLabel: 'En retard'
            },
            bookingSectionTitle: '${bookingPluralCapitalAlias}',
            rolesSectionTitle: '${rolerequestPluralCapitalAlias}',
            noColourRulesAlert: 'Aucune règle de couleur ajoutée pour'
        },
        plans: {
            manageMyPlansLabel: 'Gérer mes plans',
            newPlanLabel: 'Nouveau plan',
            privatePlansColumnTitle: 'Mes plans',
            copyPlanLabel: 'Copier le plan',
            readOnlyLabel: 'Lecture seule',
            editAccessLabel: 'Modifier l\'accès',
            renameLabel: 'Renommer',
            deleteLabel: 'Supprimer',
            moveToPublicLabel: 'Passer en public',
            makeCopyLabel: 'Créer une copie',
            makePublicCopyLabel: 'Créer une copie publique',
            makePrivateCopyLabel: 'Créer une copie privée',
            moveToPrivateLabel: 'Passer en privé',
            privatePlansLabel: 'Plans privés',
            publicPlansLabel: 'Plans publics',
            manageMyWorkspacesLabel: 'FR_Manage My Workspaces_FR',
            newWorkspaceLabel: 'FR_New Workspace_FR',
            privateWorkspacesColumnTitle: 'FR_My Workspaces_FR',
            privateWorkspacesLabel: 'FR_Private Workspaces_FR',
            publicWorkspacesLabel: 'FR_Public Workspaces_FR',
            copyWorkspaceLabel: 'FR_Copy workspace_FR',
            editWorkspaceLabel: 'FR_Edit workspace_FR'
        },
        recordsList: {
            addBookingToResourceRecordListCaption: 'S\'inscrire ${entityAlias}',
            addBookingToJobRecordListCaption: 'S\'inscrire le ${entityAlias}',
            searchLabel: 'Rechercher',
            sortLabel: 'Trier',
            sortyByLabel: 'Trier par',
            columnsLabel: 'Colonnes',
            applyButtonText: 'Appliquer',
            detailsLabel: 'détails',
            notLoadedLabel: 'non chargé',
            notLoadedValueLabel: 'valeur non chargée',
            historyFieldPlaceholder: 'Non spécifié',
            pastLabel: 'passé',
            lastLoginLabel: 'Dernière connexion',
            expandAndCollapseText: 'Agrandir et réduire la ligne',
            expandAllCaption: 'Tout agrandir',
            collapseAllCaption: 'Tout réduire',
            sortLabelButton: 'Trier ${order}',
            resourcesLabel: 'FR_Resources_FR',
            jobsLabel: 'FR_Jobs_FR',
            calculatingSortCalcFields: 'FR_Sorting by ${fieldAlias} can take longer if there are many ${entity} to calculate for_FR',
            numberResults: 'FR_${rowCount} results_FR',
            resourceLabel: 'FR_Resource_FR',
            jobLabel: 'FR_Job_FR'
        },
        tooltipContextualMenu: {
            assignmentChangeLabel: 'Changement d\'affectation',
            outsideJobDatesLabel: 'En dehors des dates de mission',
            datesConflictWithBookingLabel: 'Dates en conflit avec la réservation',
            bookingConflictLabel: 'Conflit de réservation',
            inactiveResourceLabel: ' est inactif. Choisissez une ressource active ou non affectée pour enregistrer les modifications',
            fromLabel: 'De',
            toLabel: 'À',
            noDiaryAssignmentLabel: 'Aucun journal assigné',
            selectMultipleBarsHintPrefix: '+ clic',
            selectMultipleBarsHint: 'pour faire une sélection multiple',
            splitBookingBarHintPrefix: 'Maintenir S enfoncé',
            splitBookingBar: 'pour diviser ${bookingsPluralLowerEntity}'
        },
        dateBar: {
            customDateRangeLabel: 'Plage de dates personnalisée',
            goToLabel: 'Aller à',
            todayLabel: 'Aujourd’hui',
            dayLabel: 'Jour',
            '5daysLabel': '5 jours',
            '7daysLabel': '7 jours',
            '10daysLabel': '10 jours',
            weekLabel: 'Semaine',
            '2weeksLabel': '2 semaines',
            '4weeksLabel': '4 semaines',
            '6weeksLabel': '6 semaines',
            monthLabel: 'Mois',
            '2monthsLabel': '2 mois',
            '3monthsLabel': '3 mois',
            '6monthsLabel': '6 mois',
            yearLabel: 'Année',
            customLabel: 'Personnalisé',
            weekendsLabel: 'Week-ends',
            prevLabel: 'Précédent',
            nextLabel: 'Suivant'
        },
        multiSelectionTooltip: {
            selectionTooltip: '${entityCount} ${entityAlias} sélectionné(s)',
            mixedSelectionTooltip: '${bookingsCount} ${bookingAlias} Et ${rolesCount} ${rolerequestAlias} sélectionné(s)'
        },
        multiSelectionAlert: {
            message: 'Limite atteinte',
            description: 'Vous avez atteint la limite de sélection de ${maximumItemsCount} éléments'
        }
    },
    jobsPage: {
        commandBarConfig: {
            scenarioLabel: '${rolerequestgroupSingularCapitalAlias}',
            addLabel: 'Ajouter',
            jobsLabel: 'Missions',
            filtersLabel: 'Filtres',
            manageLabel: 'Gérer',
            editLabel: 'Modifier',
            jobLabel: 'Mission',
            duplicateLabel: 'Dupliquer',
            clientLabel: 'Client',
            editDetailsLabel: 'Modifier les détails',
            baseFilterLabel: 'Afficher',
            viewAllJobsLabel: 'Tout',
            viewJobsIManageLabel: 'Je gère',
            viewJobsActionRequiredLabel: 'Action requise',
            staticMessageAddJobsMenu: 'FR_Resources can be added via User management_FR'
        }
    },
    resourcesPage: {
        commandBarConfig: {
            scenarioLabel: '${rolerequestgroupSingularCapitalAlias}',
            addLabel: 'Ajouter',
            jobsLabel: 'Missions',
            filtersLabel: 'Filtres',
            manageLabel: 'Gérer',
            editLabel: 'Modifier',
            jobLabel: 'Mission',
            duplicateLabel: 'Dupliquer',
            clientLabel: 'Client',
            editDetailsLabel: 'Modifier les détails',
            baseFilterLabel: 'Afficher',
            viewAllJobsLabel: 'Tout',
            viewJobsIManageLabel: 'Je gère',
            viewJobsActionRequiredLabel: 'Action requise'
        }
    },
    roleInboxPage: {
        commandBarConfig: {
            scenarioLabel: '${rolerequestgroupSingularCapitalAlias}',
            addLabel: 'Ajouter',
            editLabel: 'Modifier',
            editRoleByNameButtonLabel: 'Modifier ${rolerequestPluralCapitalAlias} par nom',
            editRoleByCriteriaButtonLabel: 'Modifier ${rolerequestPluralCapitalAlias} par critère',
            deleteLabel: 'Supprimer',
            filtersLabel: 'Filtres',
            rolesLabel: 'Rôles',
            showLabel: 'Afficher',
            rolefromTemplateLabel: '${rolerequestSingularCapitalAlias} à partir d\'un modèle',
            noRoleTemplatesCreatedLabel: 'Aucun modèle n\'a été ajouté',
            archiveLabel: 'Archiver',
            restartLabel: 'Redémarrer',
            rejectLabel: 'Rejeter',
            createLabel: 'Créer',
            makeLiveLabel: 'Mise en ligne',
            submitRequestLabel: 'Envoyer une demande',
            byNameSuffix: 'par nom',
            byRequirementSuffix: 'par exigences',
            unassignFromRoleLabel: 'Désaffecter de ',
            toggleShowUnassignedRoles: '${rolePluralUpperCase} non attribuées',
            toggleShowRolesByName: '${rolePluralCapitalized} par nom',
            toggleShowRolesByRequirements: '${rolePluralCapitalized} par exigences',
            manageRoleTemplatesLabel: 'Gérer les modèles ${rolerequestSingularLowerAlias}',
            publishToMarketplaceLabel: 'Publier dans ${marketplaceAlias}',
            editRolePublicationButtonLabel: 'Modifier la publication ${rolerequestSingularLowerAlias}',
            removeRolePublicationButtonLabel: 'Supprimer la publication ${rolerequestSingularLowerAlias}'
        }
    },
    marketplacePage: {
        roleCard: {
            startsOnLabel: '${rolerequestSingularCapitalAlias} commence le',
            categoryLabel: 'Catégorie',
            availabilityLabel: 'Votre disponibilité pour ce ${rolerequestSingularLowerAlias} est',
            numberOfResources: 'Nombre de ${resourcePluralLowerAlias}',
            numberOfFte: 'ETP',
            systemDetails: 'Publié le ${publishedOn} (Mis à jour le ${updatedOn})',
            pendingResourcesNeededText: '${pendingResources} nécessaires',
            defaultRoleName: 'Nouveau rôle',
            notAvailableLabel: 'Non disponible'
        },
        commandBarConfig: {
            allLabel: 'Tous',
            filtersLabel: 'Filtres',
            marketplaceLabel: 'Tableau des rôles',
            appliedAllLabel: 'Tous',
            appliedToLabel: 'J\'ai postulé à',
            availableForLabel: 'Je suis disponible pour'
        },
        entityWindow: {
            roleApplicationWithdrawn: 'La candidature a été retirée',
            roleApplicationSubmitted: 'Candidature soumise'
        }
    },
    previewEntityPage: {
        sharePopoverTitle: 'Partager ${roleAlias}'
    },
    tableViewPage: {
        commandBarConfig: {
            pageTitle: 'Affichage en tableau',
            addLabel: 'Ajouter',
            editLabel: 'Modifier',
            viewLabel: 'Afficher',
            showLabel: 'Afficher',
            increaseDateRangeLabel: 'Augmenter la plage de dates',
            decreaseDateRangeLabel: 'Réduire la plage de dates',
            dateRangeLabel: 'Plage de dates',
            goToTodayLabel: 'Aller à aujourd\'hui',
            goToDateLabel: 'Aller à la date',
            jobsLabel: 'Missions',
            resourcesLabel: 'Ressources',
            filtersLabel: 'Filtres',
            bookingLabel: 'Réservation',
            roleLabel: 'Rôle',
            jobLabel: 'Mission',
            clientLabel: 'Client',
            cutLabel: 'Couper',
            copyLabel: 'Copier',
            pasteLabel: 'Coller',
            createLabel: 'Créer',
            deleteLabel: 'Supprimer',
            manageLabel: 'Gérer',
            moreLabel: 'Plus',
            newPlanLabel: 'Nouveau plan',
            saveAsNewPlanLabel: 'Sauvegarder en tant que nouveau plan',
            manageMyPlansLabel: 'Gérer mes plans',
            privatePlansLabel: 'Plans privés',
            publicPlansLabel: 'Plans publics',
            saveChangesLabel: 'Enregistrer les modifications',
            saveChangesToPublicLabel: 'Enregistrer les modifications dans la version publique',
            noPublicPlansCreatedLabel: 'Aucun plan public n’a été créé',
            noPrivatePlansCreatedLabel: 'Aucun plan privé n’a été créé',
            customDateRangeLabel: 'Plage de dates personnalisée',
            dayLabel: 'Jour',
            '5daysLabel': '5 jours',
            '7daysLabel': '7 jours',
            '10daysLabel': '10 jours',
            weekLabel: 'Semaine',
            '2weeksLabel': '2 semaines',
            '4weeksLabel': '4 semaines',
            '6weeksLabel': '6 semaines',
            monthLabel: 'Mois',
            '2monthsLabel': '2 mois',
            '3monthsLabel': '3 mois',
            '6monthsLabel': '6 mois',
            yearLabel: 'Année',
            weekendsLabel: 'Week-ends',
            potentialConflicts: 'Afficher les conflits potentiels',
            baseFilterLabel: 'Afficher les missions',
            findResourcesLabel: 'Trouver ${resourceEntityAlias}...',
            findResourceToolTipText: 'Trouver ${resourceEntityAlias} sur la base de critères (F)',
            showMenuTooltipText: '' // This is empty to prevent the Show menu tooltip from showing on tableview page as it is not needed for now.
        },
        hideShowBarOptions: {
            hideHistoricEntityLabel: '${subRowEntityAlias} passés',
            hideFutureEntityLabel: '${subRowEntityAlias} futurs',
            hideRolesLabel: '${rolePluralCapitalized}',
            hideInactiveEntityLabel: 'Inactif ${resourcePluralCapitalized}',
            hidePastEntitiesExplanation: 'Afficher les ${subRowEntityAlias} pour lesquels il n\'y a que des ${bookingPluralLowerCase} qui se terminent aujourd’hui ou avant',
            hideFutureEntitiesExplanation: 'Afficher les ${subRowEntityAlias} pour lesquels il n\'y a que des ${bookingPluralLowerCase} qui commencent après la fin de la plage de dates visible'
        },
        selectionBar: {
            editAllButtonLabel: 'Modifier',
            deleteAllButtonLabel: 'Supprimer',
            editButtonLabel: 'Modifier',
            deleteButtonLabel: 'Supprimer',
            createButtonLabel: 'Créer',
            makeLiveSingularButtonLabel: 'Mise en ligne',
            makeLivePluralButtonLabel: 'Tout mettre en ligne',
            insufficientRightsToEditAndDelete: 'Autorisation insuffisante pour modifier/supprimer ces ${entityAlias}',
            insufficientActionRights: 'Autorisations insuffisantes pour appliquer cette action à tous les ${entityAlias}',
            selectedLabel: 'sélectionnés',
            maxBookingsSuffix: 'max'
        },
        legendLabel: 'Légende',
        barFieldsLabel: 'Champs de barre ${barSingularAlias}',
        colourSchemeLabel: 'Thème de couleur',
        customColourThemeLabel: 'Thème de couleur personnalisé',
        customColourSchemeLabel: 'Thème de couleur personnalisé ${barSingularAlias}',
        editedSuffix: 'modifié',
        createdSuffix: 'créé',
        deletedSuffix: 'supprimé',
        applyButtonText: 'Appliquer',
        searchLabel: 'Chercher',
        notLoadedLabel: 'non chargé',
        notLoadedValueLabel: 'valeur non chargée',
        legend: {
            legendTitle: 'Légende',
            coloursColumnSubTitle: 'Les couleurs des barres sont basées sur les',
            barTypes: {
                draftRoles: 'Brouillons de ${rolerequestSingularCapitalAlias}',
                roleRequestsToLiveBookings: 'Demandes de ${rolerequestSingularCapitalAlias} qui pourraient devenir des ${bookingPluralLowerAlias} actives',
                unconfirmed: 'Non confirmé',
                planned: 'Planifié',
                excludesNonWorkingDays: 'Exclut les jours non travaillés',
                includesNonWorkingDays: 'Inclut les jours non travaillés',
                inConflict: 'En conflit',
                startDateNonWorking: 'La date de début tombe dans un week-end masqué',
                endDateNonWorking: 'La date de fin tombe dans un week-end masqué',
                bothDatesNonWorking: 'Les dates de début et de fin tombent dans un week-end masqué',
                roleRequestsOnLiveBookings: '${rolerequestSingularCapitalAlias} demandes devenues actives ${bookingPluralLowerAlias}'
            },
            tabsTitlesMap: {
                colourTypesTabTitle: 'Thème de couleur',
                barTypesTabTitle: 'Types de barres',
                milestonesTabTitle: '${jobSingularAlias} détails'
            },
            bookingSectionTitle: '${bookingPluralCapitalAlias}',
            rolesSectionTitle: '${rolerequestPluralCapitalAlias}',
            noColourRulesAlert: 'Aucune règle de couleur ajoutée pour'
        },
        plans: {
            manageMyPlansLabel: 'Gérer mes plans',
            newPlanLabel: 'Nouveau plan',
            privatePlansColumnTitle: 'Mes plans',
            copyPlanLabel: 'Copier le plan',
            editPlanLabel: 'Modifier le plan',
            readOnlyLabel: 'Lecture seule',
            editAccessLabel: 'Modifier l\'accès',
            renameLabel: 'Renommer',
            deleteLabel: 'Supprimer',
            moveToPublicLabel: 'Passer en public',
            makeCopyLabel: 'Créer une copie',
            makePublicCopyLabel: 'Créer une copie publique',
            makePrivateCopyLabel: 'Créer une copie privée',
            moveToPrivateLabel: 'Passer en privé',
            privatePlansLabel: 'Plans privés',
            publicPlansLabel: 'Plans publics'
        },
        recordsList: {
            addBookingToJobRecordListCaption: 'S\'inscrire ${entityAlias}',
            addBookingToJobLabel: 'Réserver ${entityAlias} pour ${jobName}',
            addBookingToResourceRecordListCaption: 'S\'inscrire à un ${entityAlias}',
            addBookingToResourceLabel: 'Réserver ${entityAlias} pour ${resourceName}',
            searchLabel: 'Rechercher',
            sortLabel: 'Trier',
            sortyByLabel: 'Trier par',
            columnsLabel: 'Colonnes',
            applyButtonText: 'Appliquer',
            detailsLabel: 'détails',
            notLoadedLabel: 'non chargé',
            notLoadedValueLabel: 'valeur non chargée',
            historyFieldPlaceholder: 'Non spécifié',
            pastLabel: 'passé',
            lastLoginLabel: 'Dernière connexion',
            expandAndCollapseText: 'Développer et réduire la ligne',
            expandAllCaption: 'Développer tout',
            collapseAllCaption: 'Réduire tout',
            sortLabelButton: 'Trier ${order}',
            resourcesLabel: 'FR_resources_FR',
            jobsLabel: 'FR_jobs_FR',
            calculatingSortCalcFields: 'FR_Sorting by ${fieldAlias} can take longer if there are many ${entity} to calculate for_FR',
            resourceLabel: 'FR_Resource_FR',
            jobLabel: 'FR_Job_FR'
        },
        tooltipContextualMenu: {
            assignmentChangeLabel: 'Changement d\'affectation',
            outsideJobDatesLabel: 'En dehors des dates de mission',
            datesConflictWithBookingLabel: 'Dates en conflit avec la réservation',
            bookingConflictLabel: 'Conflit de réservation',
            inactiveResourceLabel: ' est inactif. Choisissez une ressource active ou non affectée pour enregistrer les modifications',
            fromLabel: 'De',
            toLabel: 'À',
            noDiaryAssignmentLabel: 'Aucun journal assigné',
            selectMultipleBarsHintPrefix: '+ clic',
            selectMultipleBarsHint: 'pour faire une sélection multiple',
            splitBookingBarHintPrefix: 'Maintenir S enfoncé',
            splitBookingBar: 'pour diviser ${bookingsPluralLowerEntity}'
        },
        dateBar: {
            goToLabel: 'Aller à la date',
            todayLabel: 'Aujourd’hui',
            dayLabel: 'Jour',
            monthLabel: 'Mois',
            '1monthsLabel': '1 mois',
            '2monthsLabel': '2 mois',
            '3monthsLabel': '3 mois',
            '4monthsLabel': '4 mois'
        }
    },
    rolegroupListPage: {
        emptyStateBoldLabel: 'Aucun scénario',
        emptyStateLabel: 'Créer et comparer des scénarios pour ce poste',
        actionRequiredLabel: 'action requise',
        rolesLabel: 'rôle',
        actionsRequiredLabel: 'actions requises'
    },
    rolegroupDetailsPage: {
        emptyStateLabel: 'Votre liste de ${rolerequestPluralLowerAlias} sera affichée ici',
        resourceInactiveString: 'Choisissez une ${resourceSingularLowerAlias} active pour faire avancer le ${rolerequestSingularLowerAlias}.',
        addRoleText: 'Ajouter un ${rolerequestSingularLowerAlias}',
        addRoleByNameLabel: '${rolerequestSingularCapitalAlias} par nom',
        addRoleByRequirementsLabel: '${rolerequestSingularCapitalAlias} par exigences',
        roleFromTemplateLabel: '${rolerequestSingularCapitalAlias} à partir d\'un modèle',
        noRoleTemplatesCreatedLabel: 'Aucun modèle n\'a été ajouté',
        notLoadedLabel: 'Non chargé',
        noValuesLoadedLabel: 'Valeur non chargée',
        applyText: 'Appliquer',
        searchText: 'Rechercher',
        manageRoleTemplates: 'Gérer les modèles ${rolerequestSingularLowerAlias}',
        actionButtonLabel: 'Actions',
        viewDetails: 'Afficher les détails',
        defaultRoleName: 'Nouveau rôle',
        noRoleGroupSetLabel: 'Aucun(e) ${rolerequestgroupSingularLowerAlias} défini(e)',
        noResourcesMeetCriteriaText: 'Essayez de modifier ou de supprimer certaines exigences, puis enregistrez les modifications.',
        noResourcesFoundAdditionalText: 'Il peut falloir jusqu\'à 24 heures pour que les données sur les compétences récemment mises à jour soient prises en compte dans les suggestions.',
        moveToButtonLabel: 'Déplacer vers...',
        noMatchesFoundTopText: 'Aucun résultat trouvé',
        unsavedChangesToRoleText: 'Modifications apportées à ${rolerequestSingularLowerAlias} et non enregistrées',
        suggestionsNotUpToDateText: 'Les suggestions ne sont pas à jour, enregistrez les modifications avant de suggérer des ${resourcePluralLowerAlias}.',
        assignResource: 'Attribuer à ${rolerequestSingularLowerAlias}',
        unassignResource: 'Désaffecter de ${rolerequestSingularLowerAlias}',
        addToShortlist: 'Ajouter à la liste restreinte',
        hiddenSuggestionsTopText: 'Suggestions masquées',
        hiddenSuggestionsText: 'Les suggestions de ${resourcePluralLowerAlias} sont masquées, car elles sont basées sur certaines exigences pour lesquelles vous ne disposez pas des autorisations suffisantes.',
        tailMessage: 'Vous n\'avez pas trouvé de concordance ? Essayez de modifier ou de supprimer certains critères liés au rôle.',
        editButtonLabel: 'Modifier ${rolerequestSingularCapitalAlias}',
        multipleRolesSelectedTopText: 'Plusieurs ${rolerequestPluralLowerAlias} sélectionnés',
        multipleRolesSelectedBodyText: 'Pour afficher les ${resourcePluralLowerAlias} proposés, sélectionner un seul ${rolerequestSingularCapitalAlias} par critère.',
        removeFromShortlist: 'Supprimer de la liste restreinte',
        shortlist: 'Présélection',
        shortlisted: 'Présélectionné',
        notShortListed: 'Non présélectionné',
        shortlistedBy: 'Présélectionné par',
        maxShortlistReachedFirstRow: 'Limité à',
        maxShortlistReachedSecondRow: '6 ressources',
        saveAsTemplateLabel: 'Enregistrer comme modèle',
        potentialConflictsTooltip: 'Conflits potentiels avec les éléments existants {bookingSingularLowerAlias}',
        maxAllowedRolesLabel: 'Maximum 100 ${rolerequestPluralLowerAlias}',
        commandBarConfig: {
            editLabel: 'Modifier',
            duplicateLabel: 'Dupliquer',
            deleteLabel: 'Supprimer'
        },
        recordsList: {
            addBookingToResourceRecordListCaption: 'S\'inscrire ${entityAlias}',
            addBookingToJobRecordListCaption: 'S\'inscrire le ${entityAlias}',
            searchLabel: 'Rechercher',
            sortLabel: 'Trier',
            sortyByLabel: 'Trier par',
            columnsLabel: 'Colonnes',
            applyButtonText: 'Appliquer',
            detailsLabel: 'détails',
            notLoadedLabel: 'non chargé',
            notLoadedValueLabel: 'valeur non chargée',
            historyFieldPlaceholder: 'Non spécifié',
            pastLabel: 'passé',
            lastLoginLabel: 'Dernière connexion',
            expandAndCollapseText: 'Agrandir et réduire la ligne',
            expandAllCaption: 'Tout agrandir',
            collapseAllCaption: 'Tout réduire',
            sortLabelButton: 'Trier ${order}',
            resourcesLabel: 'FR_Resources_FR',
            jobsLabel: 'FR_Jobs_FR',
            calculatingSortCalcFields: 'FR_Sorting by ${fieldAlias} can take longer if there are many ${entity} to calculate for_FR',
            numberResults: 'FR_${rowCount} results_FR',
            resourceLabel: 'FR_Resource_FR',
            jobLabel: 'FR_Job_FR'
        }
    },
    roleRequirementsSection: {
        addCriteriaButtonLabel: 'Ajouter des exigences',
        resourceAttributesLabel: 'Attributs de ${resourceSingularCapitalAlias}',
        mustMeetCriteriaDescription: 'Doit satisfaire',
        mustMeetCriteriaExplanation: 'Les ${resourcePluralCapitalAlias} qui ne répondent pas à ces exigences ne seront pas suggérés',
        criteriaEmptyStateMessage: 'Aucune exigence ajoutée',
        removeRequirementLabel: 'Retirer',
        resourceSkillsLabel: '${resourceSingularCapitalAlias} compétences',
        searchLabel: 'Rechercher',
        applyButtonText: 'Appliquer',
        removeSkillsText: 'Effacer des compétences',
        removeFilterText: 'Supprimer des compétences',
        levelText: 'Niveau',
        levelsText: 'Niveaux',
        anySkillsText: 'Tout',
        oneOfSkillsText: 'L\'un des...',
        saveSkillsText: 'Ajouter des compétences',
        cancelSkillsText: 'Annuler'
    },
    entityWindow: {
        basicDetailsSectionTitle: 'Détails de base',
        requirementsSectionTitle: 'Exigences',
        milestonesSectionTitle: 'Jalons',
        cMeSectionTitle: 'caractéristiques C-me',
        workHistoryTitle: 'Missions récentes',
        workDetailsSectionTitle: 'Détails de la mission',
        budgetSectionTitle: 'Budget',
        budgetDetailsSectionTitle: 'Informations budgétaires',
        timeAndFinancialsSectionTitle: 'FR_Time and financials_FR',
        revenueSectionTitle: 'FR_Revenue_FR',
        costsSectionTitle: 'FR_Costs_FR',
        profitSectionTitle: 'FR_Profit_FR',
        hoursSectionTitle: 'FR_Hours_FR',
        planningSectionTitle: 'Planification',
        previousRelatedJob: 'Poste lié précédent',
        nextRelatedJob: 'Poste lié suivant',
        contactSectionTitle: 'Contact',
        emplyomentDetailsSectionTitle: 'Informations sur l’emploi',
        skillsSectionTitle: 'Compétences',
        systemInfoSectionTitle: 'Information système',
        timeAllocationTitle: 'Heures allouées',
        projectHealthTitle: 'État du projet',
        fixedTimeSectionSuffix: 'heures',
        loadingSectionSuffix: '% d\'heures de travail',
        timeSectionSuffix: 'heures réservées au total',
        hoursInTotalSuffix: 'heures au total',
        hoursPerDaySuffix: 'heures par jour',
        FTESuffix: 'ETP',
        resourcesSuffix: '${resourcePluralLowerAlias}',
        nameLabel: 'Nom',
        updatedToLabel: 'Mis à jour à',
        fromLabel: 'de',
        numberOfResourcesPrefix: 'Nombre de ressources',
        chargeRateFieldsControlTitle: 'Taux de facturation',
        bookingResourceChargeRateLabel: 'Taux de facturation de la ressource',
        bookingOverriddenChargeRateLabel: 'Utiliser un taux de facturation différent',
        bookingCustomChargeRateLabel: 'Utiliser un taux personnalisé',
        bookingRevenueRatesRowTitle: 'Revenu',
        bookingCostRatesRowTitle: 'Coût',
        bookingProfitRatesRowTitle: 'Profit',
        bookingViewModeChargeRatesTitle: 'Taux',
        bookingOwnResourceChargeModeLabel: 'Taux de facturation de la ressource',
        bookingDifferentResourceChargeModeLabel: 'Taux de facturation différent',
        bookingCustomChargeModeLabel: 'Taux personnalisé',
        rolerequestDescriptionPlaceholder: 'p. ex. Chef de projet',
        rolerequestOwnResourceChargeModeLabel: 'Taux de facturation de la ressource',
        rolerequestDifferentResourceChargeModeLabel: 'Taux de facturation différent',
        rolerequestCustomChargeModeLabel: 'Taux personnalisé',
        rolerequestResourceChargeRateLabel: 'Taux de facturation de la ressource',
        rolerequestOverriddenChargeRateLabel: 'Utiliser un taux de facturation différent',
        rolerequestCustomChargeRateLabel: 'Utiliser un taux personnalisé',
        rolerequestRevenueRatesRowTitle: 'Revenu',
        rolerequestCostRatesRowTitle: 'Coût',
        rolerequestProfitRatesRowTitle: 'Profit',
        roleByNameWindowTitle: '${rolerequestCapitalEntityAlias} par nom',
        manageRoleTemplatesWindowTitle: 'Gérer mes modèles',
        roleTemplateWindowTitle: '${rolerequestCapitalEntityAlias} modèle',
        roleByRequirementWindowTitle: '${rolerequestCapitalEntityAlias} par exigences',
        dateRangeLabel: 'Plage de dates',
        datesRequiredLabel: 'Dates requises',
        nonWorkSectionFieldText: 'Inclure les jours non travaillés',
        bookingSectionTitle: 'Réservation',
        rolerequestSectionTitle: 'Exigences',
        rolerequestGroupSectionTitle: 'Groupe de rôles',
        jobSectionTitle: 'Mission',
        resourceSectionTitle: 'Ressource',
        clientSectionTitle: 'Client',
        bookingSectionTitlePlural: 'Réservations',
        jobSectionTitlePlural: 'Missions',
        resourceSectionTitlePlural: 'Ressources',
        clientSectionTitlePlural: 'Clients',
        rolerequestSectionTitlePlural: 'Exigences',
        rolerequestGroupSectionTitlePlural: 'Groupes de rôles',
        moreInfoButtonLabel: 'Plus d\'informations',
        duplicateLabel: 'Dupliquer',
        assignToRoleButtonLabel: 'Affecter à',
        unassignFromRoleButtonLabel: 'Désaffecter de',
        rejectButtonLabel: 'Rejeter',
        restartButtonLabel: 'Redémarrer',
        archiveButtonLabel: 'Archiver',
        editButtonLabel: 'Modifier',
        applyButtonLabel: 'Appliquer',
        closeButtonLabel: 'Fermer',
        withdrawButtonLabel: 'Retirer sa candidature',
        editRoleByNameButtonLabel: 'Modifier ${rolerequestPluralCapitalAlias} par nom',
        editRoleByCriteriaButtonLabel: 'Modifier ${rolerequestPluralCapitalAlias} par critère',
        createButtonLabel: 'Créer',
        createEntityTitle: 'Créer ${entityTitleAlias}',
        editEntityTitle: 'Modifier ${entityTitleAlias}',
        makeLiveSingularButtonLabel: 'Mise en ligne',
        makeLivePluralButtonLabel: 'Tout mettre en ligne',
        submitRequestButtonLabel: 'Envoyer une demande',
        addBookingLabel: 'Ajouter une réservation',
        cancelButtonLabel: 'Annuler',
        saveChangesButtonLabel: 'Enregistrer les modifications',
        saveAllButtonLabel: 'Sauvegarder tout',
        discardChangesButtonLabel: 'Annuler les modifications',
        progressButtonLabel: 'Progression',
        deleteButtonLabel: 'Supprimer',
        editAllButtonLabel: 'Tout modifier',
        archiveAllButtonLabel: 'Tout archiver',
        restartAllButtonLabel: 'Tout redémarrer',
        submitRequestAllButtonLabel: 'Envoyer une demande',
        deleteAllButtonLabel: 'Tout supprimer',
        newButtonLabel: 'Créer',
        viewButtonLabel: 'Afficher',
        compareButtonLabel: 'Comparer',
        createTemplateLabel: 'Créer un modèle',
        roleTemplateLabel: '${rolerequestSingularCapitalAlias} modèle',
        rolePublicationWindowTitle: '${rolerequestCapitalEntityAlias} publication',
        insufficientActionRights: 'Autorisations insuffisantes pour appliquer cette action à tous',
        addNewRoleLabel: 'Ajouter ${rolerequestSingularLowerAlias}',
        roleListBodyEmptyStateLabel: 'Vous pouvez ajouter une ${resourceSingularLowerAlias} nommée, ou saisir des exigences de ${rolerequestSingularLowerAlias} pour trouver des ${resourcePluralLowerAlias} appropriés',
        manageRoleTemplatesEmptyStateLabel: 'Vous n\'avez pas de modèles ${rolerequestSingularLowerAlias}.',
        templateDetailsLabel: 'Détails du modèle',
        provideTemplateNameLabel: 'Veuillez fournir un nom pour votre modèle',
        maxLengthValidationMessage: 'Maximum ${maxNameLength} symboles autorisés',
        renameLabel: 'Renommer',
        createdLabel: 'Créé',
        myTemplatesLabel: 'Mes modèles',
        deleteMultipleBookinngsButtonLabel: 'Supprimer',
        deleteMultipleRolerequestsButtonLabel: 'Supprimer',
        bookingStatusFieldExplanation: 'La ressource restera disponible pendant le temps réservé',
        tableViewBookingStatusFieldExplanation: 'Non confirmé ${bookingSingularLowerAlias} sera visible sur la ${plannerPageAlias} page. ${resourceSingularCapitalAlias} restera disponible aux moments réservés.',
        nonWorkSectionFieldExplanation: 'Des heures supplémentaires seront réservées les jours non travaillés',
        jobIsConfidentialFieldExplanation: 'Les emplois confidentiels ne peuvent être vus que par les personnes qui y ont accès',
        rolerequestRolerequestGroupFieldExplanation: 'Laisser ce champ vide créera un rôle en dehors d\'un scénario.',
        rolerequestFTEFieldExplanation: '1 L\'équivalent temps plein est ${referenceDiaryTime} heures par jour',
        resourceSectionFieldExplanation: 'L\'ajout de plusieurs ressources créera une réservation pour chacune d’elles',
        jumpToSectionTitle: 'Passer à',
        additionalSectionTitle: 'Section supplémentaire',
        additionalDetailsSectionTitle: 'Informations supplémentaires',
        commentsSectionTitle: 'Commentaires',
        roleGroupListSectionTitle: 'Groupes de rôles',
        detailsPaneTooltipText: 'El panel de detalles le ofrece información sobre el elemento que ha seleccionado. Los iconos le llevarán a la información sobre el trabajo, los recursos o las reservas.',
        detailsPaneTooltipTitle: 'Volet Détails',
        attachmentsSectionTitle: 'Documents',
        moreOptionsButtonLabel: 'Plus d\'options',
        bookingBudgetDetailsMessage: 'Les calculs budgétaires utilisent le taux de facturation en vigueur le premier jour de la réservation.',
        entityCreatedSuffix: 'créé',
        entityDeletedSuffix: 'supprimé',
        notFoundPrefix: 'valeur',
        notFoundSuffix: 'non trouvé',
        roleMarketplaceCriteriaMatchExplanation: 'Les candidats doivent répondre aux exigences.',
        rolerequestDiaryForEstimationLabel: 'Agenda pour l\'estimation',
        selectChargeRateLabel: 'Sélectionner un taux de facturation',
        customChargeRateLabel: 'Taux de facturation personnalisé',
        estimatedBudgetLabel: 'Budget estimé',
        estimatesTabLabel: 'Estimer',
        assignedTotalsTabLabel: 'Totaux attribués',
        roleGroupCountLabel: '${roleGroupCount} ${rolerequestgroupPluralCapitalAlias}',
        messages: {
            bookingBudgetDetailsMessageText: 'Les calculs budgétaires utilisent le taux de facturation en vigueur le premier jour de la ${bookingSingularLowerAlias}',
            roleBudgetDetailsMessageText: 'Les calculs budgétaires utilisent le taux de facturation en vigueur le premier jour de ${rolerequestSingularLowerAlias}.',
            roleAssigneesTotalsDifferenceText: 'Les totaux réels peuvent différer si les cessionnaires ont des journaux ou des taux de facturation différents pour l’estimation.',
            bookingMultipleResourcesBudgetDetailsMessageText: `Les taux budgétaires spécifiques à chaque \${bookingSingularLowerAlias} peuvent être consultés dans leurs détails après la création.
            Les calculs budgétaires utilisent le taux de facturation en vigueur le premier jour de la \${bookingSingularLowerAlias}.`,
            roleResourceWarningText: 'Attribuez une ${resourceSingularLowerAlias}pour demander une ${bookingSingularLowerAlias}.',
            roleResourcesContainUnassigedWarningText: `Les demandes de \${bookingSingularCapitalAlias} ne peuvent pas progresser avec
            Non attribuée \${resourcePluralLowerAlias}, Veuillez ignorer la sélection de \${resourceSingularLowerAlias} non attribuées.`,
            criteriaRoleUnassignedResourceText: 'Les informations budgétaires seront calculées lorsque les ${resourcePluralLowerAlias} seront attribués au ${rolerequestSingularLowerAlias}.',
            requirementSectionInsufficientPermissionsText: 'Certaines exigences sont masquées car vous ne disposez pas des autorisations suffisantes.',
            rolerequestCriteriaDPSuggestionPaneMessageText: 'Attribuer ${resourcePluralLowerAlias} au ${rolerequestSingularLowerAlias} via le champ !{Suggestion pane}.',
            suggestionPaneButtonText: 'Volet Suggestion',
            criteriaRoleAssignResourceText: 'Attribuer ${resourcePluralLowerAlias} au ${rolerequestSingularLowerAlias} via le volet Suggestion.',
            criteriaRoleAssignedResourceChangeMessageText: 'Modifier l\'affectation de ${rolerequestSingularLowerAlias} via le volet Suggestion.',
            criteriaRoleAssignResourceToCalculateBudgetText: 'Pour calculer le budget, il faut attribuer ${resourcePluralLowerAlias} au ${rolerequestSingularLowerAlias}.',
            criteriaRolePublishMessageText: 'Le poste ${rolerequestSingularLowerAlias} se terminera automatiquement après la date de fin ${rolerequestSingularLowerAlias} ${endDate}',
            roleApplicationAppliedOnText: 'Vous avez postulé à ce ${rolerequestSingularLowerAlias} le ${applyDate}.',
            bookingJobOverBudgetMessageText: 'Ce ${bookingSingularLowerAlias} engendrera un dépassement du budget ${jobSingularLowerAlias}.',
            bookingResourceOverBudgetMessageText: 'Réserver ce ${resourceSingularLowerAlias} entraînera un dépassement de budget pour ce ${jobSingularLowerAlias}.',
            bookingJobHoursOverBudgetMessageText: 'FR_This ${bookingSingularLowerAlias} will put this ${jobSingularLowerAlias}\'s Total hours at ${jobHoursPercentageBudget}% of its Budget hours_FR'
        },
        financialInformationSectionTitle: 'Informations financières',
        schedulingSectionTitle: 'Planification',
        rolesSectionTitle: 'Rôles',
        resourceSummaryTitle: 'Résumé',
        overlappingBookingsTitle: 'Chevauchement des enregistrements et des rôles',
        saveAsADraft: 'Enregistrer en tant que brouillon',
        backToSuggestionLabel: 'Retour aux suggestions',
        suggestLabel: 'Suggérer',
        suggestedLabel: 'Suggéré',
        forLabel: 'pour',
        moveButtonLabel: 'Déplacer',
        moveToModalTitle: 'Déplacer vers...',
        searchForLabel: 'Rechercher un(e)',
        lastRefreshedText: 'Dernière mise à jour',
        SelectionTitleLabel: 'Mise à jour en masse de tout ',
        SelectionDescriptionLabel: 'Définir les valeurs ou laisser vide pour effacer les valeurs',
        SelectionFieldsCaptionLabel: 'Champs',
        shortlistUptoSixText: 'Vous pouvez présélectionner jusqu\'à 6 ressources.',
        manageBudgetLabel: 'Gérer le budget',
        movePendingFTELabel: 'Déplacer ETP en attente',
        removePendingFTELabel: 'Supprimer ETP en attente',
        movePendingResourcesLabel: 'Déplacer ressources en attente',
        removePendingResourcesLabel: 'Supprimer ressources en attente',
        publishToMarketplaceLabel: 'Publier dans ${pageAlias}',
        publishRoleLabel: 'Publier ${rolerequestSingularLowerAlias}',
        roleMarketplaceCategoryPlaceholder: 'Ajouter une catégorie',
        saveAsTemplateLabel: 'Enregistrer comme modèle',
        editRolePublicationButtonLabel: 'Modifier la publication ${rolerequestSingularLowerAlias}',
        removeRolePublicationButtonLabel: 'Supprimer la publication ${rolerequestSingularLowerAlias}',
        emptyState: {
            noRoleGroupItemsCoincidenceMessage: 'Pour afficher les détails de ${rolerequestgroupSingularLowerAlias}, sélectionnez les ${rolerequestPluralLowerAlias} qui proviennent du même ${rolerequestgroupSingularLowerAlias}.',
            noRoleGroupItemsCoincidenceContent: 'Plusieurs ${rolerequestgroupPluralLowerAlias} dans la sélection'
        },
        createScenarioLabel: 'Créer ${rolerequestgroupSingularLowerAlias}',
        editScenarioLabel: 'Modifier ${rolerequestgroupSingularLowerAlias}',
        openScenarioButtonLabel: 'Ouvrir ${rolerequestgroupSingularCapitalAlias}',
        fieldValueCaption: {
            budget: 'FR_of budget_FR',
            target: 'FR_of target_FR'
        },
        reviewSettings: {
            headerTitle: 'FR_Review settings_FR',
            saveChangesButtonLabel: 'FR_Save changes_FR',
            cancelBtnLabel: 'FR_Cancel_FR',
            resourceSkillsReviewLabel: 'FR_Resource skills reviews_FR',
            reviewerOnThisJobLabel: 'FR_Reviewer on this job_FR',
            eligibleForReviewLabel: 'FR_Eligible for reviews_FR',
            reviewerOnThisJobCaptionLabel: 'FR_Choose who can review resource skills on this job_FR',
            pastBookingLabel: 'FR_Only resources with past booking on this job_FR',
            allBookedLabel: 'FR_All booked resources on this job_FR',
            clearSkillsReviewsLabel: 'FR_Clear all skills reviews_FR'
        }
    },
    resourceSummarySection: {
        availabilityText: 'Disponibilité',
        suitabilityText: 'Adéquation',
        hoursSuffix: 'h',
        andLabel: 'et'
    },
    suggestedResources: {
        addToShortlist: 'Ajouter à la liste',
        matchTextSuffix: 'correspondance',
        isATextMessage: 'est une ',
        skillsLabel: 'Compétences',
        workExperienceLabel: 'Expérience de travail',
        skillWithPrefix: 'Compétence avec',
        similarityTextSuffix: 'similarité :',
        suitabilityText: 'Adéquation',
        mixedSortOptionText: 'Adéquation et disponibilité',
        sortOrderText: 'Ordre de tri : ',
        suggestionsLabel: 'Suggestions',
        forLabel: 'pour',
        appliedOn: 'Demande faite le',
        lastRefreshedText: 'Dernière mise à jour',
        refreshLabel: 'Réactualiser',
        loadingLabel: 'Chargement en cours...',
        noRelevantAISuggestionDataText: 'Données de compétence ou d\'expérience insuffisantes pour un score d\'adéquation',
        infoBannerMessage: 'Tous les résultats répondent aux exigences de ${resourceSingularLowerAlias} l\'attribut.',
        aiSuggestionsLabel: 'Suggestions de l’IA',
        aiToggleTooltipText: 'Activer ce bouton pour utiliser les suggestions de l’IA'
    },
    common: {
        selectRowTextAriaLabel: 'Sélectionner la ligne pour ${name}',
        maximumFieldLengthValidationMessage: '{maximumFieldSize} caractères maximum',
        daysString: 'jours',
        singleDayString: 'jour',
        workingString: 'travaillé',
        nonWorkingString: 'non travaillé',
        hoursString: 'heures',
        FTEstring: 'ETP',
        pendingString: 'en attente',
        hoursPerDaySuffix: 'heures par jour',
        excludeNonWorkingString: 'exclut les jours non travaillés',
        includeNonWorkingString: 'inclut les jours non travaillés',
        addPrefix: 'Ajouter',
        newPrefix: 'Nouveau',
        allPrefix: 'Tout',
        addAnotherPrefix: 'Ajouter autre',
        clickToEditSuffix: '(cliquez pour modifier)',
        insufficientPermissionsToEditSuffix: 'Autorisations insuffisantes pour modifier',
        historyFieldPlaceholder: 'Non spécifié',
        noValueMessage: 'Pas de ${fieldInfoAlias} défini(e)',
        milestoneHistoryFieldPlaceholder: 'Aucun jalon fixé',
        startingLabel: 'Début',
        endingLabel: 'Fin',
        dueDate: 'Date d\'échéance',
        nameLabel: 'Nom',
        restrictedLabel: 'Restreint',
        markAsCompletedLabel: 'Marquer comme terminé',
        milestonesSectionTitle: 'Jalons',
        milestonesSectionTitleNameDueDate: 'Date d’échéance du nom de jalon',
        milestonePlaceholder: 'par exemple, approbation du projet',
        noString: 'Non',
        setString: 'défini',
        totalSuffix: 'Total',
        hourlySuffix: 'horaire',
        noResultsFoundMessage: 'Aucun résultat trouvé',
        noResultsMessage: 'Aucun résultat',
        checkedMessage: 'Oui',
        uncheckedMessage: 'Non',
        shownLabel: 'Afficher',
        hiddenLabel: 'Masquer',
        jobUtitilizationInfo: 'Exclure les réservations de l\'utilisation',
        excludeValue: 'Exclure',
        includeValue: 'Inclure',
        noResultsMessagePrefix: 'Non',
        noResultsMessageSuffix: 'portant ce nom existe.',
        noOptionsSetSuffix: 'options de catégorie définies par votre administrateur',
        showMorePrefix: 'Afficher',
        showMoreSuffix: 'plus',
        showLessText: 'Afficher moins',
        seeMoreText: 'FR_See more_FR',
        detailsTabLabel: 'Détails',
        historyTabLabel: 'Historique',
        editAllTabLabel: 'Tout modifier',
        roleListLabel: 'Liste des rôles',
        newTemplateLabel: 'Nouveau modèle',
        audit: {
            sortLabel: 'Trier',
            showMoreText: 'Afficher plus',
            backToTopText: 'Retour en haut de page',
            oldValuePrefix: 'de',
            actorPrefix: 'par',
            startingText: 'début',
            timeLineActionCreateAlias: 'ajouté',
            timeLineActionUpdateAlias: 'mis à jour',
            unassignedValue: 'Non attribué',
            levelString: 'niveau',
            timeLineActionUpdateSuffixAlias: 'à',
            timeLineActionDeleteAlias: 'retiré',
            timeLineActionRemoveAlias: 'retiré',
            falseString: 'Non',
            trueString: 'Oui',
            anyLevelString: 'Tous les niveaux',
            resourceNotFoundCaption: '${resourceSingularCapitalAlias} non trouvé',
            templateTexts: {
                historyRecordCreateText: '${alias}: ${valueDescription} ${startingText} ${valueStartDate} ${timeLineActionCreateAlias}',
                historyRecordUpdateText: '${alias} ${timeLineActionUpdateAlias} ${timeLineActionUpdateSuffixAlias} ${valueDescription} ${oldValuePrefix} ${oldValueDescription} ${startingText} ${valueStartDate}',
                historyRecordDeleteText: '${alias}: ${valueDescription} ${startingText} ${valueStartDate} ${timeLineActionRemoveAlias}'
            },
            auditSectionTitles: {
                projectHealthTitle: 'État du projet :'
            }
        },
        lastUpdatedLabel: 'Dernière mise à jour',
        updatedByLabel: 'par',
        noDiaryOnDatesLabel: 'Aucun journal',
        noOverlappingBookings: 'Aucun chevauchement de réservations',
        archivedLabel: 'Archiver',
        restartedLabel: 'Redémarrer',
        requestedLabel: 'Demander',
        batchRequestedLabel: 'demandes',
        rejectedLabel: 'Rejeter',
        expiredLabel: 'Expirer',
        draftLabel: 'Brouillon',
        liveLabel: 'Actif',
        actionsDropdownLabel: 'Actions',
        unassignedPlaceholder: 'Non attribué',
        availabilityText: 'Disponibilité',
        movePendingFTELabel: 'Déplacer ETP en attente',
        removePendingFTELabel: 'Supprimer ETP en attente',
        movePendingResourcesLabel: 'Déplacer en attente de ${resourcePluralLowerAlias}',
        removePendingResourcesLabel: 'Supprimer ressources en attente',
        rolerequestLoadingExplanation: 'de la capacité de ${resourcePluralLowerAlias}',
        noRateSetLabel: 'Aucun taux fixé',
        resourcesString: '${resourcePluralLowerAlias}',
        potentialConflictsTooltip: 'Conflits potentiels avec ${booking} existant',
        addFiltersButtonLabel: 'Ajouter des filtres',
        singleDayUnit: 'jour',
        daysUnit: 'jours',
        singleWeekUnit: 'semaine',
        weeksUnit: 'semaines',
        singleMonthUnit: 'mois',
        monthsUnit: 'mois',
        singleYearUnit: 'année',
        yearsUnit: 'années',
        revertToStartAndEndDates: 'Revenir aux dates de début et de fin',
        searchToSelect: 'Rechercher pour sélectionner',
        fieldMandatoryText: 'Ce champ est obligatoire',
        newEntityLabel: '${newPrefix} ${tableAlias}',
        estimatesTooltip: 'Estimation du budget ${rolerequestSingularLowerAlias} avant l\'attribution des ressources.',
        assigneesTotalsTooltip: 'Budget ${rolerequestSingularLowerAlias} réel avec les attributions actuelles.',
        editLabel: 'Bouton Modifier',
        closeLabel: 'Bouton Fermer',
        deleteLabel: 'Bouton Supprimer',
        addLabel: 'Bouton Ajouter',
        quantityAria: 'Entrer ${fieldAlias}',
        criteriaRoleBudgetDescription: 'Les valeurs estimées sont utilisées dans les totaux budgétaires ${rolerequestgroupSingularLowerAlias} jusqu\'à ce que la ${rolerequestSingularLowerAlias} soit entièrement pourvue.',
        noResourcesAssigned: 'Aucun(e) ${resourcePluralLowerAlias} attribué(e)',
        noRoleGroupSetLabel: 'Aucun(e) ${rolerequestgroupSingularLowerAlias} défini(e)',
        avatarAltText: 'Photo de profil pour ${resourceName}',
        copyOfPrefix: 'Copie de',
        ascendingSort: 'Croissant',
        descendingSort: 'Décroissant',
        selectionListSort: 'Trier la liste de sélection',
        deleteLabelText: 'Supprimer',
        deleteLabelItemText: 'Supprimer ${item}',
        scrollDownText: 'Faire défiler vers le bas',
        scrollBarText: 'Barre de défilement',
        scrollUpText: 'Faire défiler vers le haut',
        cancelChangesLabel: 'Annuler les modifications',
        saveChangesLabel: 'Enregistrer les modifications',
        fullScreenButtonLabel: 'vue pleine page',
        clearNameLabel: 'Effacer le champ ${fieldName}',
        collapseLeftNavigation: 'Réduire la navigation de gauche',
        expandLeftNavigation: 'Développer la navigation de droite',
        fieldAriaLabel: 'Entrée ${fieldAlias}',
        selectAllRolesLabel: 'Sélectionner tous les rôles',
        expandRowLabel: 'Développer la ligne',
        collapseRowLabel: 'Réduire la ligne',
        floatingActionBarLabel: 'FR_${entity} ${fieldName} may be different for this date range_FR',
        floatingActionBarButtonLabel: 'FR_Update sort order_FR'
    },
    tableOptions: {
        displayDensityOptionTitle: 'Densité d\'affichage',
        chooseDetailsOptionTitle: 'Choisir les détails',
        compactDensityOptionTitle: 'Compact',
        defaultDensityOptionTitle: 'Par défaut',
        expandedDensityOptionTitle: 'Étendu',
        searchLabel: 'Rechercher',
        applyButtonText: 'Appliquer',
        notLoadedLabel: 'non chargé',
        notLoadedValueLabel: 'valeur non chargée',
        tableOptionsLabel: 'Options de tableau'
    },
    dataGrid: {
        sortLabel: 'Trier',
        sortyByLabel: 'Tirer par',
        pageOptionSuffix: ' par page',
        noItemsMessage: 'Vous n’avez aucune mission',
        noRolesItemsMessage: 'Vous n’avez aucun rôle',
        newJob: 'Nouvelle mission',
        noMatchingItemsMessage: 'Aucune mission trouvée avec ces filtres',
        noMatchingResourceItemsMessage: 'FR_No resources found matching your filters_FR',
        noMatchingResourceItemsContentMessage: 'FR_Try changing your filters or view settings_FR',
        noMatchingRolesItemsMessage: 'Aucun rôle trouvé avec ces filtres',
        noMatchingItemsContent: 'Essayez de modifier les filtres.',
        noChargetypeSet: 'Aucun type de frais défini',
        noRoleGroupItemsMessage: 'Aucun scénario',
        noRoleGroupItemsContent: 'Créer et comparer des scénarios pour ce poste',
        noResourceFoundMessage: 'Aucun ${resourceEntityAlias} trouvé correspondant aux critères',
        noMarketplaceRolesPublished: 'Aucun(e)  publié(e)${rolerequestPluralLowerAlias}',
        showingCaption: 'Montrer ${pageRolesCount} sur ${totalRolesCount} ${rolerequestAlias}',
        entityPageOptionSuffix: '${rolerequestPluralLowerAlias} par page',
        noResultsMessage: 'Aucun résultat trouvé',
        tryAdjustingFiltersMessage: 'Essayez d\'ajuster votre recherche ou votre filtre',
        sortLabelButton: 'Trier ${order}',
        sortAscending: 'Croissant',
        sortDescending: 'Décroissant',
        operationLogEmptyMessage: 'Le journal d\'opérations est vide',
        operationLogEmptyContent: 'Ce journal est actuellement vide et ne comporte aucune opération enregistrée.',
        operationLogSuccess: 'Opération complète',
        operationLogIncomplete: 'Opération terminee avec des exceptions',
        operationLogCancelled: 'Opération annulée par',
        operationLogFailed: 'Échec d\'opération',
        cancelOperation: 'Annuler l’opération',
        undoOperation: 'Annuler l\'opération'
    },
    filterPane: {
        anyLevelLabel: 'N\'importe quel niveau',
        selectLabel: 'Sélectionner',
        filterSuffix: 'afficher',
        headingTitle: 'Filtres',
        applyButtonText: 'Appliquer',
        discardChangesText: 'Réinitialisation des filtres',
        applyButtonTooltipText: 'Pour permettre l’activation, veuillez ajouter des filtres et renseigner toutes les données.',
        resetButtonTooltipText: 'Remettre les filtres à leur état initial.',
        showMoreButtonText: 'Montrer plus',
        maxDateRangeMessage: 'Maximum de 5 plages de dates',
        startDateLabel: 'Début',
        endDateLabel: 'Fin',
        fromDateLabel: 'De',
        toDateLabel: 'À',
        searchLabel: 'Chercher',
        searchToSelectLabel: 'Rechercher pour sélectionner',
        textOperatorLabel: 'Comme',
        loadingLabel: 'chargement en cours',
        betweenLabel: 'entre',
        clearFiltersText: 'Effacer les filtres',
        removeFilterText: 'Retirer le filtre',
        notLoadedLabel: 'non chargé',
        notLoadedValueLabel: 'valeur non chargée',
        levelText: 'Niveau',
        levelsText: 'Niveaux',
        resetFilterText: 'Réinitialiser tout',
        clearAllFiltersText: 'Effacer tout',
        numberResults: '${rowCount} résultats',
        allLabel: 'Tout',
        yesLabel: 'Oui',
        noLabel: 'Non',
        addLabel: 'Ajouter ${fieldAlias}',
        removeLabel: 'Retirer ${fieldAlias}',
        removeFilterButtonLabel: 'Supprimer la plage de dates du ${startDate} au ${endDate}',
        typeHereLabel: 'Saisir ici',
        hiddenFiltersMessage: 'Certains filtres n\'ont pas pu être appliqués. Cela peut être dû à vos autorisations utilisateur ou à un champ/valeur non valide. En sauvegardant cet espace de travail',
        hiddenFiltersBoldMessage: 'vous supprimerez tous les filtres masqués.',
        operators: {
            DB_OPERATORS: {
                LESS_THAN: 'Inférieur à',
                LESS_THAN_OR_EQUAL: 'Inférieur ou égal à',
                EQUALS: 'Égal à',
                GREATER_THAN_OR_EQUAL: 'Supérieur ou égal à',
                GREATER_THAN: 'Supérieur à',
                LIKE: 'Comme',
                CONTAINS: 'Contient'
            },
            NUMERIC_OPERATORS_ALIAS: {
                LessThan: 'inférieur à',
                LessThanOrEqual: 'au plus',
                Equals: 'égal à',
                GreaterThanOrEqual: 'au moins',
                GreaterThan: 'supérieur à'
            },
            NUMERIC_PARAMETER_OPERATORS_ALIAS: {
                LessThan: 'inférieur à',
                LessThanOrEqual: 'au plus',
                Equals: 'égal à',
                GreaterThanOrEqual: 'au moins',
                GreaterThan: 'supérieur à'
            },
            TEXT_OPERATORS_ALIAS: {
                Like: 'contient'
            },
            MULTY_VALUES_OPERATORS_ALIAS: {
                Contains: 'fait partie de'
            },
            DATE_OPERATORS_ALIAS: {
                LessThanOrEqual: 'jusqu\'à',
                GreaterThanOrEqual: 'à partir de'
            },
            DATE_SENSITIVE_OPERATORS_ALIAS: {
                GreaterThanOrEqual: 'au moins',
                LessThanOrEqual: 'au plus'
            },
            SKILL_OPERATORS_ALIAS: {
                Equals: 'est'
            },
            CHECKBOX_OPERATORS_ALIAS: {
                Equals: 'le'
            },
            BOOLEAN_OPERATORS_ALIAS: {
                Equals: 'est'
            }
        },
        advancedFilterOperators: {
            TEXT: {
                Contains: 'Contient',
                IsBlank: 'Est vide',
                IsNotBlank: 'N’est pas vide'
            },
            NUMERIC: {
                Is: 'Est',
                IsNot: 'N’est pas',
                IsMoreThan: 'Est supérieur à',
                IsLessThan: 'Est inférieur à',
                IsBlank: 'Est vide',
                IsNotBlank: 'N’est pas vide'
            },
            BOOLEAN: {
                Is: 'Est',
                IsNot: 'N’est pas'
            },
            SKILL: {
                Is: 'Est',
                IsNot: 'N’est pas',
                IsBlank: 'Est vide',
                IsNotBlank: 'N’est pas vide'
            }
        },
        logicalOperators: {
            and: 'Et',
            or: 'Ou'
        },
        uiFilterOperators: {
            Is: 'est',
            IsNot: 'n’est pas',
            IsMoreThan: 'est supérieur à',
            IsLessThan: 'est inférieur à',
            Contains: 'contient',
            UpTo: 'est inférieur ou égal',
            From: 'est supérieur ou égal',
            IsNotBlank: 'n’est pas vide',
            IsBlank: 'est vide',
            IsMoreOrEqual: 'est supérieur ou égal',
            IsLessOrEqual: 'est inférieur ou égal'
        },
        filterFieldMessages: {
            resource_has_skill_resource_skill_levelAlias: 'Compétences',
            resource_guidAlias: 'Nom de la ressource',
            availabilityAlias: 'Disponibilité',
            utilisationAlias: 'Utilisation',
            departmentAlias: 'resource_current_department_guidDepartment',
            resource_manager_resource_guidAlias: 'Superviseur direct',
            resource_location_guidAlias: 'Site',
            resource_localgrade_guidAlias: 'Classe',
            resource_resourcetype_guidAlias: 'Type d\'emploi',
            resource_rolenameAlias: 'Intitulé',
            resource_booking_countAlias: 'Compte de réservation',
            'Charge RateAlias': 'Taux de facturation',
            job_guidAlias: 'Nom de la mission',
            job_startAlias: 'Début de la mission',
            job_endAlias: 'Fin de la mission',
            job_client_guidAlias: 'Client',
            job_engagementlead_resource_guidAlias: 'Superviseur direct',
            job_location_guidAlias: 'Site de la mission',
            job_jobstatus_guidAlias: 'État de la mission',
            job_codeAlias: 'Code de référence de la mission',
            job_chargetype_guidAlias: 'Type de frais',
            booking_is_assignedAlias: 'Non attribué',
            booking_startAlias: 'Début de la réservation',
            booking_endAlias: 'Fin de la réservation',
            booking_bookingtype_guidAlias: 'État de la réservation',
            booking_notesAlias: 'Remarques de la réservation',
            booking_workactivity_guidAlias: 'Activité professionnelle',
            updatedonAlias: 'mis à jour le',
            updatedbyAlias: 'mis à jour par ressource',
            createdonAlias: 'créé le',
            createdbyAlias: 'créé par ressource'
        }
    },
    talentProfilePage: {
        profileTitle: 'Mon profil',
        shareProfileCaption: 'FR_Share profile_FR',
        uploadLabel: 'Importer des documents',
        changeProfielPictureText: 'Modifier la photo de profil',
        viewOtherProfile: 'Voir autre profil',
        viewMyProfile: 'Voir mon profil',
        cMeProfileTitle: 'Profil C-Me',
        editDetailsLabel: 'FR_Edit details_FR',
        updateAvatarWindowMessages: {
            headerTitle: 'Mettre à jour la photo de profil',
            applyBtnTitle: 'Appliquer',
            removeBtnTitle: 'Retirer la photo',
            cancelBtnTitle: 'Annuler',
            uploadAreaText: 'Cliquez sur un fichier ou faites-le glisser dans cette zone pour l’importer',
            fileTypesString: 'Types de fichier : ',
            dragControlLabel: 'Glisser',
            zoomControlLabel: 'Zoom'
        },
        recommendationTitle: 'Recommandations',
        recommendationAlertHeader: 'Mettez vos compétences à jour et gagnez en visibilité',
        recommendationAlertDescription: 'Voici quelques compétences que vous pourriez ajouter à votre profil',
        skillApproval: 'FR_Your changes will be sent to manager for approval. Changes to skill preferences do not require approval._FR',
        additionalDetailsSectionTitle: 'Informations supplémentaires',
        messages: {
            fileTooLargeLabel: 'L’importation du document a échoué : le fichier est trop volumineux',
            fileTypeForbiddenLabel: 'L’importation du document a échoué : le type de fichier n’est pas autorisé',
            noFilesUploadedLabel: 'Vous n’avez aucun document importé',
            uploadsLimitReachedLabel: 'Limite de documents atteinte : supprimez des documents pour en importer d’autres'
        },
        uploadDocumentsWindowMessages: {
            headerTitle: 'FR_Upload document_FR',
            uploadBtnTitle: 'FR_Upload_FR',
            cancelBtnTitle: 'FR_Cancel_FR',
            uploadAreaText: 'FR_Click or drag file to this area to upload_FR',
            fileTypesString: 'FR_Accepted formats: _FR',
            documentType: 'FR_Type of document_FR',
            expiryDate: 'FR_Expiry date_FR',
            maxFileSize: 'FR_Max ${maxFileSize} MB per file_FR'
        }
    },
    prompts: {
        createOrSaveAsNewPlanPrompt: {
            title: 'Nouveau plan',
            placeholder: 'Créer un plan',
            helpMessage: 'Veuillez indiquer un nom pour votre plan',
            okText: 'Créer un plan',
            cancelText: 'Annuler',
            name: 'nom',
            type: 'Type',
            access: 'Accès',
            editAccess: 'Modifier l\'accès',
            readOnlyAccess: 'Lecture seule',
            subHeading: 'Sauvegarder en tant que nouveau plan privé ou public',
            privatePlan: 'plan privé',
            publicPlan: 'plan public',
            newPlanLabel: 'Nouveau plan',
            maxLengthValidationMessage: 'Maximum de symboles ${maxNameLength} autorisés',
            switchOnLabel: 'Allumer'
        },
        createOrSaveAsNewWorkspacePrompt: {
            title: 'FR_New workspace_FR',
            placeholder: 'FR_Insert workspace name_FR',
            helpMessage: 'FR_Please provide a name for your workspace_FR',
            okText: 'Créer un plan',
            cancelText: 'Annuler',
            name: 'nom',
            type: 'Type',
            access: 'Accès',
            editAccess: 'Modifier l\'accès',
            readOnlyAccess: 'Lecture seule',
            subHeading: 'FR_Save as a new private or public workspace_FR',
            privatePlan: 'FR_Private workspace_FR',
            publicPlan: 'FR_Public workspace_FR',
            maxLengthValidationMessage: 'Maximum de symboles ${maxNameLength} autorisés',
            newPlanLabel: 'FR_New Workspace_FR',
            switchOnLabel: 'Allumer'
        },
        extendJobRangePrompt: {
            okText: 'Oui, modifier les dates de mission',
            cancelText: 'Non, annuler le déplacement',
            title: 'Prolonger la mission ?',
            message: 'Souhaitez-vous prolonger la période de la mission pour permettre le déplacement de cette réservation ?',
            detailsText: 'Nouvelles dates de mission pour'
        },
        deleteBookingPrompt: {
            title: 'Supprimer',
            message: 'Voulez-vous supprimer ce(tte)',
            okText: 'Oui, supprimer le(la)',
            cancelText: 'Non, conserver le(la)',
            noEntityDescriptionPrefix: 'Non',
            noEntityDescriptionSuffix: 'description'
        },
        makePlanPrivatePrompt: {
            okText: 'Rendre le plan privé',
            cancelText: 'Garder le plan public',
            makeString: 'Créer',
            privatePlanString: 'un plan privé',
            planTypeMessageStart: 'Ce plan est actuellement',
            messageFirstPart: 'Si vous rendez ce plan privé,',
            messageBold: 'il ne sera plus à la disposition des autres.'
        },
        makeWorkspacePrivatePrompt: {
            okText: 'FR_Make workspace private_FR',
            cancelText: 'FR_Keep workspace public_FR',
            makeString: 'Créer',
            privatePlanString: 'FR_a private workspace_FR',
            planTypeMessageStart: 'FR_This workspace is currently_FR',
            messageFirstPart: 'FR_If you make this workspace private,_FR',
            messageBold: 'il ne sera plus à la disposition des autres.'
        },
        deletePlanPrompt: {
            okText: 'Oui, supprimer le plan',
            cancelText: 'Non, conserver le plan',
            deleteString: 'Supprimer',
            planString: 'plan',
            workspaceTypeMessage: 'Ce plan est actuellement',
            publicDeleteMessageStart: 'Si vous supprimez ce plan',
            publicDeleteMessageEnd: 'il ne sera plus à la disposition des autres',
            question: 'Souhaitez-vous supprimer définitivement ce plan ?'
        },
        deleteWorkspacePrompt: {
            okText: 'FR_Yes, delete workspace_FR',
            cancelText: 'FR_No, keep workspace_FR',
            deleteString: 'Supprimer',
            planString: 'FR_workspace_FR',
            workspaceTypeMessage: 'FR_This workspace is currently_FR',
            publicDeleteMessageStart: 'FR_If you delete this workspace_FR',
            publicDeleteMessageEnd: 'il ne sera plus à la disposition des autres',
            question: 'FR_Do you wish to permanently delete this workspace?_FR'
        },
        deleteRoleTemplatePrompt: {
            okText: 'Oui, supprimer le modèle',
            cancelText: 'Non, conserver le modèle',
            warning: 'Cette action ne peut être annulée.',
            question: 'Souhaitez-vous supprimer définitivement ce modèle ${rolerequestSingularLowerAlias} ?',
            title: 'Supprimer le modèle ${roleTemplateDescription} ?'
        },
        renameRoleTemplatePrompt: {
            okText: 'Renommer le modèle',
            cancelText: 'Garder l\'ancien nom',
            question: 'Vous avez renommé le modèle. Voulez-vous enregistrer cette modification ?',
            title: 'Renommer le modèle',
            fromText: 'de ',
            toText: 'à '
        },
        deleteClientPrompt: {
            title: 'Supprimer le client ?',
            message: 'La suppression de ce client le retirera définitivement de l\'ensemble du système, y compris de toutes les missions associées. Il ne sera pas possible d’affecter ce client à de futures missions. \n Souhaitez-vous continuer ?',
            okText: 'Oui, supprimer le client',
            cancelText: 'Non, conserver le client'
        },
        deleteJobPrompt: {
            title: 'Supprimer',
            okText: 'Supprimer',
            cancelText: 'Conserver',
            thereString: 'Il',
            isString: 'y a',
            areString: 'y a',
            andString: 'et',
            onString: 'le',
            thisString: 'ce(tte)',
            theseString: 'ces',
            betweenString: 'entre',
            onThisJobString: 'sur cette mission',
            withString: 'avec',
            deletingThisJobWillAlsoDeleteString: 'La suppression de cette tâche supprimera également',
            doYouWishToPermanentlyDeleteString: 'Souhaitez-vous supprimer définitivement',
            messages: {
                deleteJobTitle: 'Supprimer ${jobDescription}?',
                deleteJobLabel: 'Supprimer ${jobAlias}, ${bookingAlias}, ${roleGroupAlias} et ${roleRequestAlias}?'
            }
        },
        deleteRolePrompt: {
            title: 'Supprimer',
            message: 'Voulez-vous supprimer ce(tte)',
            okText: 'Oui, supprimer le(la)',
            cancelText: 'Non, conserver le(la) ',
            noEntityDescriptionPrefix: 'Non',
            noEntityDescriptionSuffix: 'description',
            defaultRoleName: 'Nouveau rôle',
            noRoleGroupSetLabel: 'Aucun(e) ${rolerequestgroupSingularLowerAlias} défini(e)'
        },
        removeRolePublicationPrompt: {
            title: 'Retirer ${rolerequestSingularLowerAlias} de ${roleBoardPageAlias}',
            message: '${countOfResources}${resourcePluralLowerAlias} sont appliqués à ${rolerequestSingularLowerAlias}. Supprimer le ${rolerequestSingularLowerAlias} de ${roleBoardPageAlias} supprimera également leurs candidatures.',
            question: 'Voulez-vous supprimer ${rolePublicationDescription} de ${roleBoardPageAlias} ?',
            confirmation: 'Supprimer ${resourcePluralLowerAlias} candidatures et ${rolerequestSingularLowerAlias} de ${roleBoardPageAlias} ?',
            okText: 'Supprimer ${rolerequestSingularLowerAlias}',
            cancelText: 'Garder ${rolerequestSingularLowerAlias}',
            defaultRoleName: 'Nouveau rôle'
        },
        unsavedChangesPrompt: {
            message: 'La page présente des modifications non enregistrées. Voulez-vous tout de même la quitter ?',
            saveLabel: 'Enregistrer les modifications',
            discardLabel: 'Annuler les modifications',
            cancelLabel: 'Annuler'
        },
        renamePlanPrompt: {
            okText: 'Renommer le plan',
            cancelText: 'Conserver l\'ancien nom',
            title: 'Renommer le plan',
            message: 'Vous avez renommé le plan. Souhaitez-vous enregistrer cette modification ?',
            oldNamePrefix: 'de',
            newNamePrefix: 'à'
        },
        renameWorkspacePrompt: {
            okText: 'FR_Rename workspace_FR',
            cancelText: 'Conserver l\'ancien nom',
            title: 'FR_Rename workspace_FR',
            message: 'FR_You have renamed the workspace. Do you wish to save this change?_FR',
            oldNamePrefix: 'de',
            newNamePrefix: 'à'
        },
        saveChangesPrompt: {
            okText: 'Continuer sans enregistrer',
            cancelText: 'Annuler',
            title: 'Modifications non enregistrées',
            message: 'est un',
            planSuffix: 'plan',
            saveAsPrivatePlanButtonLabel: 'Enregistrer en mode privé',
            saveChangesToPublicButtonLabel: 'Enregistrer en mode public'
        },
        saveWorkspaceChangesPrompt: {
            okText: 'Continuer sans enregistrer',
            cancelText: 'Annuler',
            title: 'Modifications non enregistrées',
            message: 'est un',
            planSuffix: 'FR_workspace_FR',
            saveAsPrivatePlanButtonLabel: 'Enregistrer en mode privé',
            saveChangesToPublicButtonLabel: 'Enregistrer en mode public'
        },
        deleteResourceSkillPrompt: {
            okText: 'Oui, supprimer la compétence',
            cancelText: 'Non, conserver la compétence',
            selectedSkill: 'Compétence sélectionnée',
            title: 'Retirer la compétence ?',
            messagePrefix: 'Voulez-vous retirer',
            messageSuffix: ' du profil ?'
        },
        deleteCommentPrompt: {
            okText: 'Supprimer le commentaire',
            cancelText: 'Conserver le commentaire',
            title: 'Supprimer le commentaire ?'
        },
        singleCreateBookingErrorPrompt: {
            title: 'Erreur lors de la création de ${entitySingularLower}',
            message: 'Une erreur s\'est produite lors de la création du ${entitySingularLower} suivant',
            insufficientPermissionsMessage: 'Autorisations insuffisantes pour créer ce ${entitySingularLower}',
            retryButtonLabel: 'Réessayer',
            editButtonLabel: 'Modifier ${entitySingularLower}',
            discardButtonLabel: 'Supprimer ${entitySingularLower}'
        },
        singleCreateJobErrorPrompt: {
            title: 'Erreur lors de la création de ${entitySingularLower}',
            message: 'Une erreur s\'est produite lors de la création du ${entitySingularLower} suivant',
            insufficientPermissionsMessage: 'Autorisations insuffisantes pour créer ce ${entitySingularLower}',
            retryButtonLabel: 'Réessayer',
            editButtonLabel: 'Modifier ${entitySingularLower}',
            discardButtonLabel: 'Supprimer ${entitySingularLower}'
        },
        singleCreateClientErrorPrompt: {
            title: 'Erreur lors de la création de ${entitySingularLower}',
            message: 'Une erreur s\'est produite lors de la création du ${entitySingularLower} suivant',
            insufficientPermissionsMessage: 'Autorisations insuffisantes pour créer ce ${entitySingularLower}',
            retryButtonLabel: 'Réessayer',
            editButtonLabel: 'Modifier ${entitySingularLower}',
            discardButtonLabel: 'Supprimer ${entitySingularLower}'
        },
        singleCreateRolegroupErrorPrompt: {
            title: 'Erreur lors de la création de ${entitySingularLower}',
            message: 'Une erreur s\'est produite lors de la création du ${entitySingularLower} suivant',
            insufficientPermissionsMessage: 'Autorisations insuffisantes pour créer ce ${entitySingularLower}',
            retryButtonLabel: 'Réessayer',
            editButtonLabel: 'Modifier ${entitySingularLower}',
            discardButtonLabel: 'Supprimer ${entitySingularLower}'
        },
        singlePublishRoleErrorPrompt: {
            title: 'Erreur de publication ${entitySingularLower}',
            message: 'Une erreur s\'est produite lors de la publication du ${entitySingularLower} suivant :',
            insufficientPermissionsMessage: 'Autorisations insuffisantes pour publier ce ${entitySingularLower} dans ${marketplaceAlias}',
            retryButtonLabel: 'Réessayer',
            editButtonLabel: 'Modifier les détails de publication',
            discardButtonLabel: 'Annuler ${entitySingularLower}'
        },
        singleEditRolePublicationErrorPrompt: {
            title: 'Erreur lors de la modification de la publication ${entitySingularLower}',
            message: 'Une erreur s\'est produite lors de la diffusion de la publication ${entitySingularLower} suivante :',
            insufficientPermissionsMessage: 'Permissions insuffisantes pour modifier cette publication ${entitySingularLower}',
            retryButtonLabel: 'Réessayer',
            discardButtonLabel: 'Annuler ${entitySingularLower}'
        },
        singleRemoveRolePublicationErrorPrompt: {
            title: 'Erreur lors de la suppression de la publication ${entitySingularLower}',
            message: 'Une erreur s\'est produite lors de la suppression de la publication ${entitySingularLower} suivante :',
            insufficientPermissionsMessage: 'Autorisations insuffisantes pour supprimer cette publication ${entitySingularLower} de ${marketplaceAlias}',
            retryButtonLabel: 'Réessayer',
            discardButtonLabel: 'Annuler ${entitySingularLower}'
        },
        singleEditBookingErrorPrompt: {
            title: 'Erreur lors de la modification de ${entitySingularLower}',
            message: 'Une erreur s\'est produite lors de la modification du ${entitySingularLower} suivant',
            insufficientPermissionsMessage: 'Autorisations insuffisantes pour modifier ce ${entitySingularLower}',
            retryButtonLabel: 'Réessayer',
            editButtonLabel: 'Modifier ${entitySingularLower}',
            discardButtonLabel: 'Supprimer ${entitySingularLower}'
        },
        singleEditJobErrorPrompt: {
            title: 'Erreur lors de la modification de ${entitySingularLower}',
            message: 'Une erreur s\'est produite lors de la modification du ${entitySingularLower} suivant',
            insufficientPermissionsMessage: 'Autorisations insuffisantes pour modifier ce ${entitySingularLower}',
            retryButtonLabel: 'Réessayer',
            editButtonLabel: 'Modifier ${entitySingularLower}',
            discardButtonLabel: 'Supprimer ${entitySingularLower}'
        },
        singleEditClientErrorPrompt: {
            title: 'Erreur lors de la modification de ${entitySingularLower}',
            message: 'Une erreur s\'est produite lors de la modification du ${entitySingularLower} suivant',
            insufficientPermissionsMessage: 'Autorisations insuffisantes pour modifier ce ${entitySingularLower}',
            retryButtonLabel: 'Réessayer',
            editButtonLabel: 'Modifier ${entitySingularLower}',
            discardButtonLabel: 'Supprimer ${entitySingularLower}'
        },
        singleEditResourceErrorPrompt: {
            title: 'Erreur lors de la modification du profil',
            message: 'Une erreur s\'est produite lors de la modification du profil suivant :',
            insufficientPermissionsMessage: 'Autorisations insuffisantes pour modifier ce profil',
            retryButtonLabel: 'Réessayer',
            editButtonLabel: 'Modifier le profil',
            discardButtonLabel: 'Supprimer le profil'
        },
        singleSaveTemplateErrorPrompt: {
            title: 'Erreur lors de la création du modèle ${entitySingularLower}',
            message: 'Une erreur s\'est produite lors de la création du modèle ${entitySingularLower} suivant :',
            insufficientPermissionsMessage: 'Autorisations insuffisantes pour créer le modèle ${entitySingularLower}',
            retryButtonLabel: 'Réessayer',
            discardButtonLabel: 'Abandonner le modèle ${entitySingularLower}',
            defaultRoleTemplateName: 'Nouveau ${entitySingularUpper}'
        },
        singleDeleteErrorPrompt: {
            title: 'Erreur lors de la suppression du modèle ${entitySingularLower}',
            message: 'Une erreur s\'est produite lors de la suppression du modèle ${entitySingularLower} suivant :',
            insufficientPermissionsMessage: 'Autorisations insuffisantes pour supprimer le modèle ${entitySingularLower}',
            retryButtonLabel: 'Réessayer',
            discardButtonLabel: 'Abandonner le modèle ${entitySingularLower}',
            defaultRoleTemplateName: 'Nouveau ${entitySingularUpper}'
        },
        singleEditErrorPrompt: {
            title: 'Erreur lors de la modification du modèle ${entitySingularLower}',
            message: 'Une erreur s\'est produite lors de la modification du modèle ${entitySingularLower} suivant :',
            insufficientPermissionsMessage: 'Autorisations insuffisantes pour modifier le modèle ${entitySingularLower}',
            retryButtonLabel: 'Réessayer',
            discardButtonLabel: 'Abandonner le modèle ${entitySingularLower}',
            defaultRoleTemplateName: 'Nouveau ${entitySingularUpper}'
        },
        singleEditRolegroupErrorPrompt: {
            title: 'Erreur lors de la modification de ${entitySingularLower}',
            message: 'Une erreur s\'est produite lors de la modification du ${entitySingularLower} suivant',
            insufficientPermissionsMessage: 'Autorisations insuffisantes pour modifier ce ${entitySingularLower}',
            retryButtonLabel: 'Réessayer',
            editButtonLabel: 'Modifier ${entitySingularLower}',
            discardButtonLabel: 'Supprimer ${entitySingularLower}'
        },
        singleDeleteBookingErrorPrompt: {
            title: 'Erreur lors de la suppression de ${entitySingularLower}',
            message: 'Une erreur s\'est produite lors de la suppression du ${entitySingularLower} suivant',
            insufficientPermissionsMessage: 'Autorisations insuffisantes pour supprimer ce ${entitySingularLower}',
            retryButtonLabel: 'Réessayer',
            editButtonLabel: 'Modifier ${entitySingularLower}',
            discardButtonLabel: 'Supprimer ${entitySingularLower}'
        },
        singleDeleteJobErrorPrompt: {
            title: 'Erreur lors de la suppression de ${entitySingularLower}',
            message: 'Une erreur s\'est produite lors de la suppression du ${entitySingularLower} suivant',
            insufficientPermissionsMessage: 'Autorisations insuffisantes pour supprimer ce ${entitySingularLower}',
            retryButtonLabel: 'Réessayer',
            editButtonLabel: 'Modifier ${entitySingularLower}',
            discardButtonLabel: 'Supprimer ${entitySingularLower}'
        },
        singleDeleteClientErrorPrompt: {
            title: 'Erreur lors de la suppression de ${entitySingularLower}',
            message: 'Une erreur s\'est produite lors de la suppression du ${entitySingularLower} suivant',
            insufficientPermissionsMessage: 'Autorisations insuffisantes pour supprimer ce ${entitySingularLower}',
            retryButtonLabel: 'Réessayer',
            editButtonLabel: 'Modifier ${entitySingularLower}',
            discardButtonLabel: 'Supprimer ${entitySingularLower}'
        },
        singleDeleteRolegroupErrorPrompt: {
            title: 'Erreur lors de la suppression de ${entitySingularLower}',
            message: 'Une erreur s\'est produite lors de la suppression du ${entitySingularLower} suivant',
            insufficientPermissionsMessage: 'Autorisations insuffisantes pour supprimer ce ${entitySingularLower}',
            retryButtonLabel: 'Réessayer',
            editButtonLabel: 'Modifier ${entitySingularLower}',
            discardButtonLabel: 'Supprimer ${entitySingularLower}'
        },
        carouselCreateErrorPrompt: {
            title: 'Erreur lors de la modification de ${entityString} (${failedCount})',
            successfulCountMessage: 'Modification réussie de ${succeededCount} ${entityString} sur ${attemptedCount}.',
            permissionsErrorMessage: 'Autorisations insuffisantes pour modifier ce ${entityString}',
            retry: 'Réessayer',
            errorSectionMessage: 'Une erreur s\'est produite lors de la modification du ${entityString} suivant',
            edit: 'Modifier ${entityString}',
            cancel: 'Supprimer ${entityString}',
            close: 'Fermer'
        },
        carouselRollForwardCreateErrorPrompt: {
            title: 'Erreur lors de la création de ${entityString} (${failedCount})',
            successfulCountMessage: 'Créé avec succès du ${succeededCount} de ${attemptedCount} ${entityString}.',
            permissionsErrorMessage: 'Permissions insuffisantes pour créer ce ${entityString}',
            retry: 'Réessayer',
            errorSectionMessage: 'Une erreur s\'est produite lors de la création du ${entityString} suivant',
            edit: 'Retour aux options \'Déplacement vers l\'avant\'',
            cancel: 'Échec de ${entityString} rejeté',
            close: 'Fermer'
        },
        carouselEditErrorPrompt: {
            title: 'Erreur lors de la modification de ${entityString} (${failedCount})',
            successfulCountMessage: 'Modification réussie de ${succeededCount} ${entityString} sur ${attemptedCount}.',
            permissionsErrorMessage: 'Autorisations insuffisantes pour modifier ce ${entityString}',
            retry: 'Réessayer',
            errorSectionMessage: 'Une erreur s\'est produite lors de la modification du ${entityString} suivant',
            edit: 'Modifier ${entityString}',
            cancel: 'Supprimer ${entityString}',
            close: 'Fermer'
        },
        carouselDeleteErrorPrompt: {
            title: 'Erreur lors de la suppression de ${entityString} (${failedCount})',
            successfulCountMessage: 'Suppression réussie de ${succeededCount} ${entityString} sur ${attemptedCount}.',
            permissionsErrorMessage: 'Autorisations insuffisantes pour supprimer ce ${entityString}',
            retry: 'Réessayer',
            errorSectionMessage: 'Une erreur s\'est produite lors de la suppression du ${entityString} suivant',
            edit: 'Modifier ${entityString}',
            cancel: 'Supprimer ${entityString}',
            close: 'Fermer'
        },
        batchedCreateEntityErrorPrompt: {
            successfulCountMessage: 'Création réussie de ${succeededCount} ${entityString} sur ${attemptedCount}.',
            insufficientPermissionsMessage: 'Autorisations insuffisantes pour créer ce ${entityString}',
            title: 'Erreur lors de la création de ${entityString}',
            errorSectionMessage: 'Une erreur s\'est produite lors de la création du ${entityString} suivant',
            retryButtonLabel: 'Réessayer',
            cancelButtonLabel: 'Annuler',
            editEntitiesButtonLabel: 'Modifier ${entityString}',
            discardEntitiesButtonLabel: 'Supprimer ${entityString}',
            closeDialogButtonLabel: 'Fermer',
            tryAgainMessage: 'Voulez-vous réessayer ?',
            requests: 'demandes'
        },
        deleteMultipleBookingsPrompt: {
            title: 'Supprimer ${bookingsAliasUpper} ? (${bookingsCount})',
            message: 'Souhaitez-vous supprimer ${pronounString} ${bookingsAliasLower} ?',
            okText: 'Oui, supprimer la ${bookingsAliasUpper}',
            cancelText: 'Non, conserver la ${bookingsAliasUpper}',
            close: 'Fermer',
            theseString: 'ces',
            thatString: 'ce(tte)'
        },
        cantPasteBarPrompt: {
            message: 'Sélectionnez une cellule pour y coller votre ${barAliasLower}'
        },
        deleteMultipleRolerequestsPrompt: {
            title: 'Supprimer ${rolerequestsAliasUpper} ? (${rolerequestsCount})',
            message: 'Souhaitez-vous supprimer ${pronounString} ${rolerequestsAliasLower} ?',
            okText: 'Oui, supprimer le ${rolerequestsAliasUpper}',
            cancelText: 'Non, conserver le ${rolerequestsAliasUpper}',
            close: 'Fermer',
            theseString: 'ces',
            thatString: 'ce(tte)'
        },
        moveRolerequestTimeAllocationPrompt: {
            title: 'Déplacer ${pluralFieldName} en attente de ${rolerequestDescription}',
            message: 'Toutes les demandes  en attente seront déplacées vers un nouveau Draft [Brouillon] de  , et ce(tte)  sera réglé(e) sur Live [Mis(e) en ligne].${pluralFieldName}${rolerequestsSingularLower}${rolerequestsSingularLower} Les attributaires Requested [Demandés] ne seront pas désattribués. Cette action ne peut être annulée.',
            warningMessage: 'Cette action ne peut être annulée.',
            okText: 'En attente de déplacement ${pluralFieldName}',
            cancelText: 'Laisser la demande ouverte',
            close: 'Fermer',
            FTEs: 'ETP'
        },
        removeRolerequestTimeAllocationPrompt: {
            title: 'Supprimez ${pluralFieldName} en attente de ${rolerequestDescription}',
            message: 'Les ${pluralFieldName} demandé(e)s en attente seront supprimé(e)s, et le(la) ${rolerequestsSingularLower} sera activé(e). Les attributaires Requested [Demandés] ne seront pas désattribués. Cette action ne peut être annulée.',
            warningMessage: 'Cette action ne peut être annulée.',
            okText: 'Supprimer l\'élément en attente ${pluralFieldName}',
            cancelText: 'Laisser la demande ouverte',
            close: 'Fermer',
            FTEs: 'ETP'
        },
        createRolegroupModal: {
            placeholder: 'Nouveau ${roleGroupAlias}',
            title: 'Créer un ${roleGroupAlias}',
            nameDescriptor: 'Nom',
            createLabel: 'Créer',
            cancelLabel: 'Annuler',
            currentValue: '${jobDescription} ${roleGroupAlias} ${currentSubsequentNumber}',
            helpMessage: 'Veuillez indiquer un nom pour votre ${roleGroupAlias}',
            maxLengthValidationMessage: '${maxNameLength} symboles autorisés au maximum'
        },
        saveAsTemplateModal: {
            placeholder: 'Nouveau modèle ${roleAlias}',
            title: 'Nouveau modèle',
            headerTitle: 'Nommer et enregistrer le nouveau modèle ${roleAlias}.',
            nameDescriptor: 'Nom',
            createLabel: 'Enregistrer',
            cancelLabel: 'Annuler',
            currentValue: '${rolerequestDescription}',
            defaultNewRole: 'Nouveau ${roleAlias}',
            helpMessage: 'Veuillez fournir un nom à votre modèle ${rolerequestDescription}',
            maxLengthValidationMessage: 'Maximum de ${maxNameLength} symboles autorisés'
        },
        progressRolesWindow: {
            title: 'Erreur lors de la progression de ${roleAlias}'
        },
        deleteRoleGroupPrompt: {
            title: 'Supprimer ${roleGroupDescription} ?',
            roleGroupInfoMessage: 'Il y a <bold>${rolesNumber}</bold> ${roleAliasPlural} incluant des <bold>${roleRequests}</bold> demandes dans ce ${roleGroupAliasSingular}, entre <bold>${roleStartDate} et</bold> - <bold>${roleEndDate}</bold>. La suppression de ce ${roleGroupAliasSingular} supprimera également ces ${roleAliasPlural} et ces demandes.',
            shouldDeleteQuestion: 'Voulez-vous supprimer définitivement <bold>${roleGroupDescription}</bold>?',
            checkboxText: 'Supprimer le ${roleGroupAliasSingular}, les ${roleAliasPlural} et les demandes.',
            cancelMessage: 'Conserver le ${roleGroupAliasSingular}',
            confirmMessage: 'Supprimer le ${roleGroupAliasSingular}'
        },
        extendJobRangeDetailsPagePrompt: {
            okText: 'Oui, modifier les dates de mission',
            cancelText: 'Non, annuler la planification',
            title: 'Prolonger la mission ?',
            message: 'Souhaitez-vous prolonger la période de la mission pour permettre la planification de',
            roleTailMessage: 'ce rôle ?',
            rolesTailMessage: 'ces rôles ?',
            detailsText: 'Nouvelles dates de mission pour'
        },
        singleMoveResourceRolegroupErrorPrompt: {
            title: 'Erreur de déplacement en attente ${resourcePluralLower}',
            message: 'Erreur lors du déplacement de ce(tte) ${entitySingularLower}',
            insufficientPermissionsMessage: 'Autorisations insuffisantes pour déplacer ce(tte) ${entitySingularLower}',
            retryButtonLabel: 'Réessayer',
            discardButtonLabel: 'Annuler ${entitySingularLower}'
        },
        singleRemoveResourceRolegroupErrorPrompt: {
            title: 'Erreur lors de la suppression en attente ${resourcePluralLower}',
            message: 'Erreur lors de la suppression de ce(tte) ${entitySingularLower}',
            insufficientPermissionsMessage: 'Autorisations insuffisantes pour déplacer ce(tte) ${entitySingularLower}',
            retryButtonLabel: 'Réessayer',
            discardButtonLabel: 'Annuler ${entitySingularLower}'
        },
        singleMoveFTERolegroupErrorPrompt: {
            title: 'Erreur de déplacement des ETP en attente',
            message: 'Une erreur est survenue lors du déplacement des ETP en attente pour ce(tte) ${entitySingularLower} :',
            insufficientPermissionsMessage: 'Autorisations insuffisantes pour déplacer ce(tte) ${entitySingularLower}',
            retryButtonLabel: 'Réessayer',
            discardButtonLabel: 'Annuler ${entitySingularLower}'
        },
        singleRemoveFTERolegroupErrorPrompt: {
            title: 'Erreur de suppression des ETP en attente',
            message: 'Une erreur est survenue lors de la suppression des ETP en attente pour ce(tte) ${entitySingularLower} :',
            insufficientPermissionsMessage: 'Autorisations insuffisantes pour déplacer ce(tte) ${entitySingularLower}',
            retryButtonLabel: 'Réessayer',
            discardButtonLabel: 'Annuler ${entitySingularLower}'
        },
        updateRolerequestStatusWindow: {
            title: 'Erreur de mise à jour de l\'état de ${roleAlias}'
        },
        publishRoleErrorPrompt: {
            title: 'Erreur de publication ${roleAlias}'
        },
        editRolePublicationPrompt: {
            title: 'Erreur lors de la modification de la publication ${roleAlias}'
        },
        withdrawRoleApplicationPrompt: {
            title: 'Confirmer le retrait',
            question: 'Votre candidature ne sera plus prise en considération pour ce ${entitySingularLower}.',
            warning: 'Voulez-vous vraiment retirer votre candidature ?',
            okText: 'Oui, retirer ma candidature',
            cancelText: 'Non, maintenir ma candidature'
        },
        tableViewHoursValidationPrompt: {
            title: 'Heures non valides',
            message: 'Vous pouvez réserver entre 0 et 168 heures par semaine.',
            okText: 'OK',
            tooltipText: 'Appuyez sur Échap pour fermer'
        },
        tableViewCellEditErrornPrompt: {
            title: 'Erreur lors de la sauvegarde des modifications',
            errorMessage: 'La mise à jour de la page ${tableViewPageAlias} peut requérir une combinaison de création, de modification et de suppression de ${bookingPluralForm} en arrière-plan.',
            contactMessage: 'Veuillez contacter votre administrateur pour vérifier si vous avez les autorisations requises.',
            discardText: 'Annuler les modifications',
            retryText: 'Réessayer'
        },
        splitBookingsErrorPrompt: {
            title: 'Erreur lors de la sauvegarde des modifications',
            question: 'Il se peut que le ${bookingSingularLowerAlias} sélectionné ait été supprimé ou modifié par quelqu\'un d\'autre, ou vous ne disposez pas des autorisations suffisantes. La division de ${bookingPluralLowerAlias}requiert une combinaison de création et modification de ${bookingPluralLowerAlias} en arrière-plan.',
            warning: 'Veuillez rafraîchir la page et réessayez, ou contactez votre administrateur.',
            cancelText: 'OK'
        },
        setPasswordConfirmationPrompt: {
            title: 'Confirmation requise',
            question: 'La modification du mot de passe invalidera les intégrations existantes. Confirmer ?',
            okText: 'Oui, je confirme',
            cancelText: 'Non, revenir en arrière'
        },
        duplicateJobErrorPrompt: {
            title: 'Erreur lors de l\'enregistrement des modifications',
            errorMessage: 'Cela peut être dû à une ou plusieurs des raisons suivantes :',
            suggestedActions: [
                'Ce ${jobSingularLowerAlias}n\'a pas de ${bookingPluralLowerAlias} dans la plage de dates sélectionnée',
                'La file de de duplication de ${jobSingularLowerAlias} est pleine',
                'Vous ne disposez pas d\'autorisations suffisantes pour le ${bookingPluralLowerAlias} sélectionné, car la duplication d\'un ${jobSingularLowerAlias} nécessite la création de ${bookingPluralLowerAlias} en arrière-plan'
            ],
            contactMessage: 'Actualisez la page et réessayez, ou contactez votre administrateur.',
            okText: 'OK'
        },
        duplicateRoleGroupErrorPrompt: {
            title: 'Erreur lors de la création ${rolerequestPluralLowerAlias}',
            errorMessage: 'Vous n\'avez pas les autorisations suffisantes pour créer ces ${rolerequestPluralLowerAlias}.',
            contactMessage: 'Veuillez rafraîchir la page et réessayer, ou contacter votre administrateur.',
            okText: 'Réessayer',
            cancelText: 'Annuler'
        },
        deleteOperationLogPrompt: {
            title: 'Effacer les lignes créées lors de cette opération ?',
            shouldDeleteQuestion: 'Souhaitez-vous effacer définitivement les lignes créées lors de cette opération ? Si de nouveaux emplois sont créés, des recrutements, les scénarios ou les postes créés pour ces emplois seront également effacés.',
            checkboxText: 'Effacer les lignes créées lors de cette opération',
            cancelMessage: 'Conserver les lignes',
            confirmMessage: 'Effacer les lignes'
        },
        confirmMassDuplicatePrompt: {
            title: 'Dupliquer',
            message: 'Votre opération a été mise en file d\'attente. Vous pouvez suivre son statut dans le Opérations.',
            closeText: 'OK'
        },
        updateSeriesBookingPrompt: {
            message: 'Ces réservations seront supprimées et recréées sur la base des informations mises à jour.',
            okText: 'Continuer',
            cancelText: 'Annuler'
        }
    },
    comments: {
        editedFlag: 'modifié',
        editButtonLabel: 'Modifier',
        deleteButtonLabel: 'Supprimer',
        confirmEditButtonLabel: 'Mettre à jour',
        cancelButtonLabel: 'Annuler',
        createButtonLabel: 'Ajouter un commentaire',
        createPlaceholder: 'Commencez à saisir un commentaire',
        showMoreButtonLabel: 'Afficher plus de commentaires'
    },
    navigation: {
        title: 'Aide',
        contactSupport: 'Contacter l’assistance',
        helpPageLink: 'Documents d\'aide',
        keyboardShortcuts: 'Raccourcis clavier',
        legend: 'Légende',
        operationLogButtonLabel: 'Opérations',
        notifications: 'Notifications',
        settings: 'Réglages',
        logout: 'Se déconnecter'
    },
    skills: {
        noAddedSkills: 'Aucune compétence ajoutée',
        noRecommendations: 'Aucune nouvelle recommandation',
        recommendationTitle: 'Recommandations',
        mySkillsLabel: 'FR_My skills_FR',
        approvalRequestsLabel: 'FR_Approval requests_FR',
        approvalRequestSent: 'FR_Approval request sent_FR',
        noSkillPendingRequestsLabel: 'FR_No skill update requests_FR',
        noSkillApprovalHistoryLabel: 'FR_No skill approval historic requests_FR',
        skillApprovalHistoryLabel: 'FR_Historic requests are automatically removed after 1 year_FR',
        skillTagLabel: 'Mot-clé',
        skillCategoryLabel: 'Catégories',
        defaultSelectedSection: 'Tous les types de compétences',
        noMatchingSkillsText: 'Aucune compétence correspondante trouvée.',
        searchPlaceholder: 'Rechercher une compétence',
        saveButtonLabel: 'Ajouter des compétences',
        cancelButtonLabel: 'Annuler',
        headerTitle: 'Ajouter des compétences',
        primarySaveButtonLabel: 'Enregistrer',
        skillsToAddSectionName: 'Compétences à ajouter',
        addSkillsButtonLabel: 'Ajouter des compétences',
        editSkillsButtonLabel: 'Modifier les compétences',
        skillsSectionTitle: 'Compétences',
        expandAllCaption: 'Tout agrandir',
        collapseAllCaption: 'Tout réduire',
        singularSkillString: 'compétence',
        pluralSkillsString: 'compétences',
        markDeletedMessage: 'Marqué pour suppression. La suppression aura lieu lorsque vous confirmerez les modifications.',
        cancelDeletionMessage: 'Annuler la suppression',
        skillLevelDeletedMessage: 'Le niveau de compétence a été supprimé pour cette compétence. Un nouveau niveau doit être défini.',
        validationRequiredText: 'est requis',
        validationLessThanText: 'ne peut pas être inférieur à',
        validationGreaterThanText: 'ne peut pas être supérieur à',
        validationIntegerNumberText: 'doit être un nombre entier',
        maxCharactersPrefix: 'Maximum',
        maxCharactersSuffix: 'caractères',
        tagsPrefixText: 'Mots clés :',
        markedForDeletionMessage: 'Marqué pour suppression. La suppression aura lieu lorsque vous confirmerez les modifications.',
        deleteLabel: 'Supprimer ${skillName}',
        cancelDeletionSkillLabel: 'Annuler la suppression de ${skillName}',
        noValueMessage: 'Pas de ${fieldInfoAlias} défini(e)',
        insufficientPermissionsToEditSuffix: 'Autorisations insuffisantes pour modifier',
        searchSkillFilterCascaderPlaceholder: 'FR_Select skills and levels french_FR',
        noManagerToApproveMessage:'FR_You do not have manager to approve your skills_FR'
    },
    pages: {
        plannerPage: 'Plans',
        adminSettings: 'Paramètres',
        jobsPage: 'Missions',
        talentProfile: 'Profil de talent',
        translation: 'Mon profil',
        report: 'Rapport',
        logout: 'Déconnexion',
        collapseText: 'Replier',
        expandText: 'Déplier',
        errorMessages: {
            goBackText: 'Revenir à votre dernière page',
            goToText: 'Aller à',
            errorCodeMessagePrefix: 'Erreur',
            defaultHeaderText: 'Un problème est survenu',
            closeText: 'Fermer',
            '401': {
                headerText: 'Non autorisé.',
                message: 'Votre session a expiré. Veuillez vous reconnecter.'
            },
            '403': {
                headerText: 'Accès refusé.',
                message: 'Autorisations insuffisantes.'
            },
            '404': {
                headerText: 'Nous sommes désolés.',
                message: 'Nous n\'arrivons pas à trouver la page que vous recherchez.'
            },
            error: {
                headerText: 'Nous sommes désolés.',
                message: 'Une erreur semble être survenue.',
                errorCodeMessage: 'Un problème s’est produit. Essayez de recharger la page.'
            },
            calculateFteError: {
                headerText: 'Impossible de calculer le FTE',
                message: 'Veuillez sélectionner un journal de référence FTE valide pour cette mission.'
            }
        }
    },
    manageEntityLookupWindow: {
        searchForLabel: 'Rechercher un(e)',
        showLessLabel: 'Afficher moins',
        showMoreLabels: {
            prefix: 'Afficher',
            suffix: 'plus'
        },
        noResultsMessagePrefix: 'Non',
        noResultsMessageSuffix: 'n’a été trouvé avec ce nom.'
    },
    contextualMenu: {
        createEntity: 'Créer',
        editEntity: 'Modifier',
        deleteEntity: 'Supprimer',
        copyEntity: 'Copier',
        rollForwardEntity: 'Dupliquer',
        cutEntity: 'Couper',
        pasteEntity: 'Coller',
        clearEntity: 'Effacer',
        setDateRange: 'Définir une plage de dates',
        loadingCaption: '...chargement en cours',
        restart: 'Redémarrer',
        archive: 'Archiver',
        reject: 'Rejeter',
        makeLive: 'Mise en ligne',
        submitRequest: 'Envoyer une demande',
        rollForwardTooltipText: 'Copier l’entité ${bookingEntityAlias} sélectionnée vers une autre ${jobEntityAlias} ou date',
        unassignResource: 'Désaffecter de ${rolerequestSingularLowerAlias}',
        createCriteriaRole: 'Créer ${rolerequestSingularCapitalAlias} par critère',
        createRoleByName: 'Créer ${rolerequestSingularCapitalAlias} par nom',
        movePendingResources: 'En attente de déplacement ${resourcePluralLowerAlias}',
        removePendingResources: 'Supprimer l\'élément en attente ${resourcePluralLowerAlias}',
        movePendingFTEs: 'Déplacer ETP en attente',
        removePendingFTEs: 'Supprimer ETP en attente',
        manageBudget: 'Gérer le budget',
        saveAsTemplate: 'Enregistrer comme modèle',
        showInViewLabel: 'Montrer dans la vue ${pluralViewNameAlias}'
    },
    detailsPane: {
        paneLabel: 'volet',
        inModalLabel: 'volet en modale',
        showPanePrefixLabel: 'Afficher',
        showPaneSuffixLabel: 'volet',
        suggestionsSingular: 'Suggestion',
        suggestionsPlural: 'Suggestions'
    },
    blankValues: {
        notFoundString: 'non trouvé',
        dateNotFoundString: 'Date introuvable',
        noChargeTypeSetString: 'Aucun type de frais défini',
        unspecifiedString: 'non spécifié',
        noString: 'Non',
        setString: 'défini'
    },
    hotKeysHelpWindow: {
        generalLabel: 'Général',
        helpWindowTitle: 'Raccourcis clavier',
        bookingsLabel: '${bookingPluralCapitalAlias}',
        jobsLabel: '${jobPluralCapitalAlias}',
        resourcesLabel: '${resourcePluralCapitalAlias}',
        rolesLabel: '${rolerequestPluralCapitalAlias}',
        dateManipulationLabel: 'Manipulation de dates',
        menusLabel: 'Menus',
        filtersLabel: 'Filtres',
        viewOptionsLabel: 'Voir les options',
        createLabel: 'Créer',
        helpLabel: 'Aide',
        helpDocumentationLabel: 'Documents d’aide',
        orLabel: 'OU',
        plannerPage: {
            addLabel: 'Ajouter',
            editLabel: 'Modifier',
            cutLabel: 'Couper',
            copyLabel: 'Copier',
            pasteLabel: 'Coller',
            deleteLabel: 'Supprimer',
            restartLabel: 'Redémarrer',
            archiveLabel: 'Archiver',
            rejectLabel: 'Rejeter',
            makeLiveLabel: 'Mise en ligne',
            submitRequestLabel: 'Envoyer une demande',
            expandDateRangeLabel: 'Étendre la plage de dates',
            reduceDateRangeLabel: 'Réduire la plage de dates',
            setRangeTo5DayLabel: 'Définir la plage de dates à 5 jours',
            setRangeTo7DaysLabel: 'Définir la plage de dates à 7 jours',
            setRangeTo10DaysLabel: 'Définir la plage de dates à 10 jours',
            setRangeTo2WeekLabel: 'Définir la plage de dates à 2 semaines',
            setRangeTo4WeeksLabel: 'Définir la plage de dates à 4 semaines',
            setRangeTo6WeeksLabel: 'Définir la plage de dates à 6 semaines',
            setRangeTo2MonthsLabel: 'Définir la plage de dates à 2 mois',
            setRangeTo3MonthsLabel: 'Définir la plage de dates à 3 mois',
            setRangeTo6MonthsLabel: 'Définir la plage de dates à 6 mois',
            setRangeTo1YearLabel: 'Définir la plage de dates à 1 an',
            goToTodayLabel: 'Aller à aujourd\'hui',
            addMenuLabel: 'Menu Ajouter',
            editMenuLabel: 'Menu Modifier',
            viewMenuLabel: 'Menu Afficher',
            addJobLabel: 'Ajouter une $ {jobSingularLowerAlias}',
            findResourcesLabel: 'Trouver ${resourcePluralLowerAlias}',
            defaultDensityLabel: 'Appliquer la densité d\'affichage par défaut',
            mediumDensityLabel: 'Appliquer la densité d\'affichage moyenne',
            expandedDensityLabel: 'Appliquer la densité d\'affichage étendue',
            helpWindowLabel: 'Fenêtre d\'aide',
            openLegendLabel: 'Ouvrir la légende de la palette de couleurs',
            showHideWeekendsLabel: 'Afficher/masquer les week-ends',
            showHidePotentialConflictsLabel: 'Erreur dans la mise à jour du statut de ${roleAlias}',
            addRoleByName: 'Ajouter ${rolerequestSingularCapitalAlias} par nom',
            addRoleByRequirements: 'Ajouter ${rolerequestSingularCapitalAlias} par critère',
            editRoleByNameLabel: 'Modifier ${rolerequestSingularCapitalAlias} par nom',
            editRoleByCriteriaLabel: 'Modifier ${rolerequestSingularCapitalAlias} par exigences',
            rollForwardLabel: 'Dupliquer',
            movePendingResources: 'Déplacer ressources en attente',
            removePendingResources: 'Supprimer ressources en attente',
            movePendingFTEs: 'Déplacer ETP en attente',
            removePendingFTEs: 'Supprimer ETP en attente',
            splitBookingLabel: 'Diviser ${bookingSingularLowerAlias}',
            showInViewLabel: 'Montrer dans la vue ${jobPluralLowerAlias}/${resourcePluralLowerAlias}',
            dragToSelect: 'Faites glisser pour sélectionner'
        },
        jobsPage: {
            addLabel: 'Ajouter',
            editLabel: 'Modifier',
            deleteLabel: 'Supprimer',
            addMenuLabel: 'Menu Ajouter',
            editMenuLabel: 'Menu Modifier',
            compactDensityLabel: 'Appliquer la densité d\'affichage compacte',
            defaultDensityLabel: 'Appliquer la densité d\'affichage par défaut',
            expandedDensityLabel: 'Appliquer la densité d\'affichage étendue',
            helpWindowLabel: 'Fenêtre d\'aide'
        },
        roleInboxPage: {
            movePendingResources: 'Déplacer ressources en attente',
            removePendingResources: 'Supprimer ressources en attente',
            makeLiveLabel: 'Mise en ligne',
            submitRequestLabel: 'Envoyer une demande',
            restartLabel: 'Redémarrer',
            rejectLabel: 'Rejeter',
            deleteLabel: 'Supprimer',
            archiveLabel: 'Archiver',
            addRoleByName: 'Ajouter ${rolerequestSingularLowerAlias} par nom',
            addRoleByRequirements: 'Ajouter ${rolerequestSingularLowerAlias} par critère',
            editRoleByNameLabel: 'Modifier ${rolerequestSingularCapitalAlias} par nom',
            editRoleByCriteriaLabel: 'Modifier ${rolerequestSingularCapitalAlias} par exigences',
            helpWindowLabel: 'Fenêtre d\'aide',
            publishToMarketplaceLabel: 'Publier sur le ${marketplaceAlias}'
        },
        adminSettingsPage: {
            addNewItemLabel: 'Ajouter nouvel élément',
            helpWindowLabel: 'Fenêtre d\'aide'
        }
    },
    validationMessages: {
        unableToSaveChanges: 'Impossible d\'enregistrer les modifications.Veuillez vérifier les erreurs mises en surbrillance.',
        mandatoryFieldsNotCompleted: 'Les champs obligatoires doivent être remplis',
        formHasErrors: 'Ce formulaire contient des erreurs',
        activeUserText: 'est inactive. Choisissez une ressource active ou non affectée pour enregistrer les modifications',
        fieldMandatoryText: 'Ce champ est obligatoire',
        mandatoryText: 'est obligatoire',
        minimumText: 'Minimum',
        maximiumText: 'Maximum',
        maxCharactersPrefix: 'Maximum',
        maxCharactersSuffix: 'caractères',
        selectValidText: 'Veuillez sélectionner un valide',
        invalidNamePrefix: 'Non valide',
        invalidNameSuffix: 'nom',
        integerTypeText: 'est un entier',
        maxSelectedYearText: 'L\'année sélectionnée doit être antérieure à 10 000',
        minSelectedYearText: 'L\'année sélectionnée doit être postérieure à 0000',
        startDateInvalid: 'Date de début indiquée non valide',
        jobEndBeforeJobStart: 'La date de fin ne peut pas être antérieure à la date de début',
        fteMaxValidationText: 'L\'ETP total dépasse le montant demandé',
        fteString: 'ETP'
    },
    attachmentsMessages: {
        uploadButtonLabel: 'Importer des documents',
        deleteButtonLabel: 'Supprimer',
        fileTooLargeLabel: 'L’importation du document a échoué : le fichier est trop volumineux',
        fileTypeForbiddenLabel: 'L’importation du document a échoué : le type de fichier n’est pas autorisé',
        noFilesUploadedLabel: 'Vous n’avez aucun document importé',
        uploadsLimitReachedLabel: 'Limite de documents atteinte : supprimez des documents pour en importer d’autres',
        allowedFormatsLabel: 'Les documents peuvent être dans les formats suivants ${formattedAcceptedFileTypes}',
        maxFileSizeLabel: 'La taille maximale des dossiers est de ${defaultMaxFileSizeMb}MB pour chaque document',
        maxUploadsAllowed: 'Vous pouvez télécharger un maximum de ${defaultMaxUploadsAllowed} documents'
    },
    treeSelectionMessages: {
        chargeMode: 'Mode de facturation',
        revenue: 'Revenu',
        cost: 'Coût',
        profit: 'Profit',
        dateRange: 'Plage de dates',
        timeAllocation: 'Heures allouées',
        selectFieldsCaption: 'Sélectionnez des champs',
        addButtonCaption: 'Ajouter',
        historyFieldsSuffix: '(écraser à partir de la date)'
    },
    contextualDropdown: {
        detailsLabel: 'détails',
        editLabel: 'Modifier',
        duplicateLabel: 'Dupliquer',
        viewLabel: 'Afficher',
        newLabel: 'Nouveau',
        deleteLabel: 'Supprimer',
        roleByName: '${roleSingularCapitalAlias} par nom',
        roleByRequirements: '${roleSingularCapitalAlias} par exigences',
        newRoleLabel: 'Nouveau ${roleSingularLowerAlias}',
        editDetailsLabel: 'Modifier les détails',
        archiveLabel: 'Archiver',
        restartLabel: 'Redémarrer',
        rejectLabel: 'Rejeter',
        makeLiveLabel: 'Mise en ligne',
        submitRequestLabel: 'Envoyer une demande',
        createLabel: 'Créer',
        unassignLabel: 'Désaffecter de',
        movePendingFTE: 'Déplacer ETP en attente',
        removePendingFTE: 'Supprimer ETP en attente',
        movePendingResourcesLabel: 'En attente de déplacement ${resourcePluralLowerAlias}',
        removePendingResourcesLabel: 'Supprimer l\'élément en attente ${resourcePluralLowerAlias}',
        createBookingEllipsisLabel: 'Créer ${bookingSingularCapitalAlias}...',
        createRoleByNameEllipsisLabel: 'Créer ${rolerequestSingularCapitalAlias}...',
        editEllipsisLabel: 'Modifier...',
        goToProfileEllipsisLabel: 'Aller au profil',
        copyProfileUrlEllipsisLabel: 'Copier l\'URL du profil',
        manageBudgetLabel: 'Gérer le budget',
        saveAsTemplateLabel: 'Enregistrer comme modèle',
        publishToMarketplaceLabel: 'Publier dans ${marketplaceAlias}',
        editRolePublicationButtonLabel: 'Modifier la publication ${rolerequestSingularLowerAlias}',
        removeRolePublicationButtonLabel: 'Supprimer la publication ${rolerequestSingularLowerAlias}',
        detailsJobLabel: '${jobSingularCapitalAlias} détails',
        editJobLabel: 'Modifier ${jobSingularLowerAlias}',
        duplicateJobLabel: 'Dupliquer ${jobSingularLowerAlias}',
        viewRoleRequestGroupLabel: 'Comparer ${rolerequestgroupSingularLowerAlias}',
        newRoleRequestGroupLabel: 'Créer ${rolerequestgroupSingularLowerAlias}',
        detailsResourceLabel: '${resourceSingularCapitalAlias} détails',
        openLabel: 'Ouvrir',
        moreOptionsButtonLabel: 'Plus d\'options'
    },
    progressRolesWindow: {
        totalText: 'Total des modifications de la mission',
        cancelText: 'Annuler',
        progressRoleLabel: 'Rôle de progression',
        jobLabel: 'Mission',
        roleLabel: 'Rôle',
        dateRangeLabel: 'Plage de dates',
        budgetLabel: 'Budget',
        makeLive: {
            selectMessage: 'Sélectionnez des ${rolePluralAlias} à faire passer aux ${bookingPluralAlias}',
            title: 'Mise en ligne',
            submitText: 'Mise en ligne'
        },
        submitRequest: {
            selectMessage: 'Sélectionnez des ${rolePluralAlias} à demander en tant que ${bookingPluralAlias}',
            title: 'Soumettre une demande',
            submitText: 'Envoyer une demande'
        }
    },
    progressRoleErrors: {
        alreadyLiveMsg: 'Déjà actif',
        noPermissionsMsg: 'Autorisations insuffisantes'
    },
    rejectRolesWindow: {
        title: 'Rejeter',
        submitText: 'Rejeter la demande',
        cancelText: 'Annuler',
        rejectErrorMessage: 'Un problème s’est produit. L\'opération n\'a pas pu être effectuée',
        buttonLabel: 'Fermer',
        rejectReasonText: 'Sélectionner la raison du rejet de cette demande',
        errorDialogTitle: 'Erreur dans la transition de rôle',
        customReasonPlaceholderText: 'Écrivez une raison personnalisée pour le rejet de ce rôle',
        jobLabel: 'Mission',
        roleLabel: 'Rôle',
        dateRangeLabel: 'Plage de dates',
        budgetLabel: 'Budget',
        statusLabel: 'Statut'
    },
    carousel: {
        defaultRoleName: 'Nouveau ${rolerequestSingularCapitalAlias}',
        ungrouped: 'Aucun(e) ${rolerequestgroupSingularLowerAlias} défini(e)'
    },
    rollForwardDialog: {
        title: 'Dupliquer ${bookingEntityAlias}',
        submitText: 'Créer ${bookingEntityAlias}',
        cancelText: 'Annuler',
        duplicateBooking: '${bookingSingularCapitalAlias} dupliquée',
        duplicateBookings: '${bookingPluralLowerAlias} dupliquées',
        forwardOptions: {
            alertMessage: 'Copie de ${noOfBooking} ${bookingEntityAlias}',
            destinationStartDateLabel: 'Date de début de destination',
            destinationStartDateLabelError: 'La date de début de destination est obligatoire',
            destinationStartDateLabelErrorDescription: 'Les postes relatifs à ${bookingEntityAlias} sélectionnés seront maintenus après la duplication.',
            destinationJobLabel: '${jobSingularAlias} de destination',
            destinationJobError: 'La ${jobSingularAlias} de destination est obligatoire',
            destinationBookingTypeLabel: 'Type ${bookingEntityAlias} de destination',
            destinationBookingTypeError: 'Le type de la ${bookingEntityAlias} de destination est obligatoire',
            destinaltionJobExplanation: 'Les fourchettes de ${jobSingularAlias} de destination  seront modifiées en conséquence',
            offsetExplanation: 'Dès le début de la première sélection ${bookingEntityAlias}',
            editBookingLabel: 'Modifier ${bookingEntityAlias} après duplication',
            editBookingDescription: 'Ouvre la boîte de dialogue d\'édition pour apporter des modifications supplémentaires à la nouvelle ${bookingEntityAlias}',
            valuePostfix: '${bookingEntityAlias}',
            keepBookingTypeText: 'Conserver le type de ${bookingEntityAlias} tel quel',
            onPrefix: 'Le',
            inPrefix: 'Dans'
        }
    },
    repeatBookingDialog: {
        createRepeatBooking: {
            title: 'Définir la récurrence',
            submitText: 'Enregistrer',
            cancelText: 'Annuler',
            repeatEvery: 'Répéter chaque',
            repeatUntil: 'Jusqu\'au',
            noRepeatText: 'Aucune répétition',
            positiveWholeNumberErrorMessage: 'Veuillez saisir un nombre positif entier'
        },
        editRepeatBooking: {
            title: 'Quelles réservations récurrentes souhaitez-vous modifier ?',
            selectedOnly: 'Réservation sélectionnée uniquement',
            selectedAndFuture: 'Réservations sélectionnées et suivantes',
            allBookings: 'Toutes les réservations de la série',
            actionLabel: 'Modifier la série',
            singleBookingMessage: 'Vous êtes en train de modifier une réservation individuelle au sein d\'une série.',
            singleAndFutureBookingsMessage: 'Vous modifiez cette réservation et toutes les réservations suivantes dans une série récurrente.',
            allBookingsMessage: 'Vous êtes en train de modifier toutes les réservations au sein d\'une série récurrente.',
            partOfSeriesMessage: 'Cette réservation fait partie d\'une série récurrente.',
            updateFailureMessage: 'La série de réservations n\'a pas pu être mise à jour.',
            bulkBookingMessage: 'Vous êtes en train de modifier des réservations individuelles au sein d\'une série récurrente.',
            editedSingleBookingMessage: 'Cette réservation fait partie d\'une série récurrente mais a été modifiée séparément. Certains détails pourraient différer.'
        },
        deleteRepeatBooking: {
            title: 'Quelles réservations récurrentes souhaitez-vous supprimer ?',
            cannotBeUndone: 'Cette action ne peut pas être annulée.',
            selectedOnly: 'Réservation sélectionnée uniquement',
            selectedAndFuture: 'Réservations sélectionnées et suivantes',
            allBookings: 'Toutes les réservations de la série'
        },
        doesNotRepeatText: 'aucune répétition',
        repeatsEveryText: 'se répète chaque',
        on: 'à',
        starting: 'de',
        until: 'à',
        intervalText: {
            day: 'jour',
            days: 'jours',
            week: 'semaine',
            weeks: 'semaines',
            month: 'mois',
            months: 'mois'
        },
        dayOfWeekText: {
            0: 'dimanche',
            1: 'lundi',
            2: 'mardi',
            3: 'mercredi',
            4: 'jeudi',
            5: 'vendredi',
            6: 'samedi'
        },
        dayText: 'jour',
        confirmRepeatBookingPrompt: {
            title: 'Création de réservations récurrentes',
            message: 'Votre opération a été mise en file d\'attente. Vous pouvez suivre son état d\'avancement dans le Journal des opérations.',
            closeText: 'OK'
        },
        auditTrail: {
            recurringIntervalCreated: 'Série récurrente créée avec répétition chaque',
            recurringIntervalEdited: 'Intervalle de récurrence modifié pour une répétition chaque',
            recurrentSeries: 'Série récurrente'
        },
        seriesText: 'série'
    },
    jobDuplicateDialog: {
        title: 'Dupliquer ${jobSingularLowerAlias}',
        submitText: 'Créer ${bookingPluralLowerAlias}',
        cancelText: 'Annuler',
        newPrefix: 'Nouveau',
        newEntityLabel: '${newPrefix} ${tableAlias}',
        searchToSelect: 'Rechercher pour sélectionner',
        changeRangeToIncludeBookingsString: 'Modifier la plage pour inclure tous les ${bookingPluralLowerAlias}?',
        forwardOptions: {
            destinationStartDateLabel: 'Date de début de destination',
            destinationStartDateLabelError: 'La date de début de destination est obligatoire',
            destinationStartDateLabelErrorDescription: 'Les postes relatifs des ${bookingPluralLowerAlias} sélectionnés seront maintenus',
            outOfRangeEntityExplanation: 'Il semble que certains ${bookingPluralLowerAlias} se trouvent en dehors des dates de début et de fin de ce ${jobSingularLowerAlias}.',
            rolesPositionWarning: 'Les ${rolerequestPluralLowerAlias} existants sont conservés dans l\'${jobSingularLowerAlias} sélectionné et ne sont pas dupliqués.',
            jobRangeLabelError: 'La plage de dates de l\'${jobSingularLowerAlias} de destination est obligatoire',
            maximumJobRangeMessage: 'La plage de dates l\'${jobSingularCapitalAlias} doit être dans les 24 mois',
            dateRangeValueMandatory: 'Ce champ est obligatoire',
            destinationJobLabel: 'Destination ${jobSingularLowerAlias}',
            destinationJobError: 'Le ${jobSingularLowerAlias} de destination est obligatoire',
            destinationBookingTypeLabel: 'Type de ${bookingSingularLowerAlias} de destination',
            destinaltionJobExplanation: 'Les plages d\'${jobSingularLowerAlias} de destination seront modifiées en conséquence',
            offsetExplanation: 'À partir du début du premier ${bookingSingularLowerAlias} de la plage de dates',
            dateRangeForJobLabel: 'Plage de dates pour l\'${jobSingularLowerAlias} sélectionné',
            selectedJobLabel: '${jobSingularLowerAlias} sélectionné',
            destinationBookingTypeError: 'Le type de  de destination est obligatoire ${bookingSingularLowerAlias}',
            valuePostfix: '${bookingSingularLowerAlias}',
            keepBookingTypeText: 'Conserver le type de ${bookingEntityAlias} tel quel',
            onPrefix: 'Le',
            inPrefix: 'Dans'
        }
    },
    //mass duplicate settings
    massDuplicateJobs: {
        filterTitle: 'Ajouter des filtres',
        placeholderLabel: 'Chercher par nom de ${jobAlias}...',
        modalTitle: 'Sélectionner un ${jobAlias}',
        resultSingular: '${rowCount} résultat',
        resultPlural: '${rowCount} résultats',
        upToResults: 'Jusqu\'à ${rowCount} résultats',
        massDuplicateJobsTitle: 'Copier les données',
        massDuplicateJobsFieldTitle: 'Sélectionner des postes pour une duplication en masse',
        massDuplicateJobsSubLabel: 'test',
        saveButtonLabel: 'Dupliquer des postes',
        cancelButtonLabel: 'Effacer',
        formHasErrorsMessage: 'Ce formulaire contient des erreurs',
        massDuplicateJobsInfoText: 'Filtrer les postes que vous souhaitez copier.',
        massDuplicateJobsInfoTips1: '<b>Les postes</b> des emplois sélectionnés ne seront pas copiés',
        massDuplicateJobsInfoTips2: 'Les dates relatives des emplois et des recrutements seront conservées, ainsi un recrutement qui commence 3 jours après \nla date de début de l\'emploi sera créé 3 jours après la date de début du nouvel emploi',
        massDuplicateJobsInfoTips3: 'Les postes qui ont un <i>poste lié suivant</i> <b>valable ne seront pas dupliqués</b>. Seuls leurs recrutements seront copiés \nsur le poste lié.',
        massDuplicateJobsInfoTips4: 'Les recrutements attribués à des <b>ressources inactives</b> seront copiés et leur resteront attribués',
        massDuplicateJobsTextNewBooking: 'Créer de nouveaux recrutements sur la base de',
        massDuplicateJobsNewBookingTextBookings: 'Recrutements',
        massDuplicateJobsNewBookingTextActuals: 'Réels',
        massDuplicateJobsNewBookingTipMessage: 'Crée des recrutements hebdomadaires à partir des données du relevé d\'heures',
        massDuplicateJobsTextDestinationBooking: 'Destination type de recrutement',
        massDuplicateJobsDestinationOption1: 'Recrutements prévus',
        massDuplicateJobsDestinationOption2: 'Recrutements non confirmés',
        massDuplicateJobsDestinationOption3: 'Maintenir le type de recrutement tel quel',
        massDuplicateJobsDaterangeText: 'Sélectionner les postes et les recrutements entre',
        massDuplicateJobsDaterangeTextExtra: 'Les postes qui n\'ont pas de recrutement sur cette plage de dates seront exclus',
        massDuplicateJobsNewJobsText: ' Créer de nouveau postes',
        forwardOptions: {
            valuePostfix: '${bookingSingularLowerAlias}',
            keepBookingTypeText: 'Conserver le type de ${bookingEntityAlias} tel quel',
            chooseHowJobsCreatedTitle: 'Choisir la manière de créer des postes et des recrutements',
            chooseHowJobsCreatedTitleExtra: 'Les postes à <i>Poste lié suivant</i> auront des nouvelles dates de début du recrutement décalées de la nouvelle \ndate de début d\'emploi comme pour l\'emploi initial',
            dateRangeForJobLabel: 'Postes ayant débuté entre',
            fieldMandatoryText: 'Ce champ est obligatoire',
            createNewJobsLabel: 'Créer de nouveau postes',
            inPrefix: 'Dans',
            onPrefix: 'Sur',
            inPrefixExtra: 'De la date du premier recrutement sur la \nplage sélectionnée de dates',
            onPrefixExtra: 'Les nouveaux emplois commencent à cette date. Les situations de recrutement \nsont maintenues par rapport à la date de début.',
            createNewBookingsLabel: 'Créer de nouveaux recrutements sur la base de',
            bookings: 'Recrutements',
            actuals: 'Réels',
            destinationBookingTypeLabel: 'Destination type de recrutement',
            plannedBookings: 'Recrutements prévus',
            unconfirmedBookings: 'Recrutements non confirmés',
            keepBookingType: 'Maintenir le type de recrutement tel quel',
            nextRelatedJobsLabel: 'Seuls les postes copiés avec des postes liés suivants',
            newJobNamesLabel: 'Nouveaux noms de postes',
            explanationNewJobNames: 'Si le Poste lié suivant d\'un poste initial est sélectionné, le poste ne sera pas renommé',
            newJobsNamesDefaultOption: 'Par défaut',
            newJobsNamesDefaultOptionExample: 'Exemple : Copie d\'Audit Aqua 2023',
            newJobsNamesOriginalOption: 'Utiliser le nom initial',
            newJobsNamesReplaceOption: 'Remplacer',
            newJobsNamesReplaceWithOption: 'par',
            newJobsNamesReplaceOptionExample: 'Exemple : Audit Aqua 2023 devient Audit Aqua 2024',
            newJobsNamesCantFindTextToReplaceLabel: 'Si le texte à remplacer est introuvable, le nom par défaut sera utilisé (Copie de...)'
        },
        massDuplicateJobsReviewTitle: 'Résumé : ${totalNumberOfJobs} postes sélectionnés à reporter',
        massDuplicateJobsReviewPoint1: '${totalNumberOfBookings} recrutements totaux sélectionnés',
        massDuplicateJobsReviewPoint2: '${totalNumberOfConfirmedHours} heures confirmées',
        massDuplicateJobsReviewPoint3: '${totalNumberOfUnconfirmedHours} heures non confirmées',
        massDuplicateJobsReviewPoint4: '${totalNumberOfJobsWithoutNextJob} de nouveaux postes seront créés',
        massDuplicateJobsReviewPoint5: '${totalNumberOfJobsWithNextJob} les postes associés à des Postes liés suivants n\'auront que leurs recrutements ou actuels reportés',
        massDuplicateJobsReviewSummaryButtonLabel: 'Rafraîchir le résumé'
    },
    roleGroupDuplicateDialog: {
        title: 'Dupliquer ${rolerequestgroupSingularLowerAlias}',
        submitText: 'Dupliquer ${rolerequestgroupSingularLowerAlias}',
        cancelText: 'Annuler',
        forwardOptions: {
            scenarioNameLabel: '${rolerequestgroupSingularCapitalAlias} nom',
            destinationJobLabel: 'Destination ${jobSingularLowerAlias}',
            destinationJobError: 'Le ${jobSingularLowerAlias} de destination est obligatoire',
            destinationStartDateLabel: 'Date de début de destination',
            destinationStartDateLabelError: 'La date de début de destination est obligatoire',
            destinationStartDateLabelErrorDescription: 'Les positions relatives de ${rolerequestPluralLowerAlias} seront maintenues après duplication.',
            destinaltionStartDateExplanation: 'dès le début de la première ${rolerequestSingularLowerAlias} dans la ${rolerequestgroupSingularLowerAlias}',
            newRoleGroupDescriptionLabel: 'Descriptio',
            onPrefix: 'Sur',
            inPrefix: 'Dans',
            scenarioNameError: 'Ce champ est obligatoire'
        }
    },
    peopleFinderDialog: {
        createBookingText: 'Créer ${bookingEntityAlias}',
        createRoleText: 'Créer ${roleEntityAlias} par nom',
        closeText: 'Fermer',
        filterTitle: 'Ajouter des filtres',
        infoToolTipText: 'Plage de dates active dans le plan',
        refreshButtonLabel: 'Réactualiser',
        profileUrlCopied: 'URL du profil copié',
        plannerPage: {
            title: 'Rechercher ${resourceEntityAlias}',
            emptyFinderText: 'Sélectionnez des critères de recherche ${resourceEntityAlias}',
            resourceFoundInRangeText: '${resourcePluralCapitalAlias} trouvés pour <bold>${startDate} - ${endDate}</bold> s\'afficheront ici.',
            summaryText: '<bold>${resourceCount} résultats</bold> pour range'
        },
        profilePage: {
            title: 'Voir autre profil',
            emptyFinderText: 'Sélectionnez des critères de recherche de profil',
            summaryText: '<bold>${resourceCount} résultats</bold>'
        }
    },
    jobFilterDialog: {
        filterTitle: 'Ajouter des filtres',
        placeholderLabel: 'Chercher par nom ${jobAlias}...',
        modalTitle: 'Sélectionner un ${jobAlias}',
        resultSingular: '${rowCount} résultat',
        resultPlural: '${rowCount} résultats',
        upToResults: 'Jusqu\'à ${rowCount} résultat'
    },
    operationsLogDialog: {
        heading: 'Journal d\'opérations',
        dataGridExplanations: 'Enregistre les opérations effectuées sur notre site'
    },
    expandedOperationsLogDialog: {
        heading: 'Duplication du poste',
        jobsRollForwarded: 'postes reportés',
        bookingsCreated: 'recrutements totaux créés',
        newJobsCreated: 'nouveaux postes créés',
        jobsDuplicated: 'les postes associés à des Postes liés suivants n\'ont que leurs recrutements ou actuels reportés'
    },
    actionBarWithFooterButtons: {
        saveButtonLabel: 'Enregistrer les modifications',
        cancelButtonLabel: 'Annuler',
        formHasErrorsMessage: 'Ce formulaire contient des erreurs'
    },
    cMeSection: {
        title: 'caractéristiques C-me'
    },
    educationSection: {
        formConfiguration: {
            education: 'Formation',
            dialogConfig: {
                saveText: 'Enregistrer',
                cancelText: 'Annuler'
            },
            institutionLabel: 'Institution',
            institutionError: 'Institution est un champ obligatoire',
            fieldLabel: 'Domaine',
            fieldError: 'Domaine est un champ obligatoire',
            degreeLabel: 'Diplôme',
            noResultsFoundMessage: 'Aucune option de diplôme configurée par votre administrateur',
            startDateLabel: 'Date de début',
            endDateLabel: 'Date de fin',
            endFieldError: 'La date de fin ne peut pas être antérieure à la date de début',
            endDateDescription: 'La date de fin peut désigner le mois et l’année d\'obtention du diplôme telle que prévue.',
            detailsLabel: 'Détails',
            addInstitutionPlaceholder: 'Ajouter une institution',
            addFieldPlaceholder: 'Ajouter un domaine',
            addDegreePlaceholder: 'Ajouter un diplôme',
            addDetailsPlaceholder: 'Ajouter des détails',
            maxCharErrorPrefix: 'Maximum de',
            maxCharErrorSuffix: 'caractères autorisés'
        },
        addEducationButtonLabel: 'Ajouter une formation',
        editEducationButtonLabel: 'Modifier la formation'
    },
    experienceSection: {
        formConfiguration: {
            experience: 'Expérience',
            companyLabel: 'Entreprise',
            roleLabel: 'Rôle',
            locationLabel: 'Emplacement',
            startDateLabel: 'Date de début',
            endDateLabel: 'Date de fin',
            detailsLabel: 'Détails',
            endDateFieldError: 'La date de fin ne peut pas être antérieure à la date de début',
            roleError: 'Rôle est un champ obligatoire',
            companynameError: 'Nom de l\'entreprise est un champ obligatoire',
            maxCharErrorPrefix: 'Maximum de',
            maxCharErrorSuffix: 'caractères autorisés',
            addDetailsPlaceholder: 'Ajouter des détails',
            addCompanyPlaceholder: 'Ajouter une entreprise',
            addRolePlaceholder: 'Ajouter un rôle',
            addLocationPlaceholder: 'Ajouter un emplacement',
            dialogConfig: {
                saveText: 'Enregistrer',
                cancelText: 'Annuler'
            }
        },
        addExperienceButtonLabel: 'Ajouter une expérience',
        editExperienceButtonLabel: 'Modifier l\'expérience'
    },
    cookieConsentBanner: {
        accept: 'Accepter',
        title: 'Ce site Web utilise des cookies',
        info: 'Nous utilisons des cookies pour améliorer votre expérience et à des fins d\'analyse. En cliquant sur « Accepter », vous consentez à l\'utilisation de ces cookies. Pour plus d\'informations sur la manière dont nous utilisons les cookies et pour en savoir plus sur vos droits en matière de protection de la vie privée, consultez notre <cookie>Politique relative à l\'utilisation des cookies</cookie> et notre <privacy>Politique de protection de la vie privée</privacy>.'
    },
    banners: {
        maintenanceStatusBanner: {
            dismissLabel: 'Ignorer',
            singleDayInfo: '<bold>Maintenance prévue sur ${startDate}, de ${startTime} à ${endTime}</bold>. Retain sera temporairement indisponible pendant que nous améliorons notre site.',
            multiDayInfo: '<bold>Maintenance prévue de ${startDate}, à ${startTime} jusqu\'à ${endDate}, à ${endTime}</bold>. Retain sera temporairement indisponible pendant que nous améliorons notre site.'
        },
        jobsPageBookmarkBanner: {
            dismissLabel: 'FR_Dismiss_FR',
            description: 'FR_We have changed the URL of this page. Update your bookmark if needed_FR'
        }
    },
    lastLoginSection: {
        minutesAgo: '${timeAgo}il y a un mois',
        hoursAgo: '${timeAgo}il y a une heure',
        daysAgo: '${timeAgo}il y a un jour',
        now: 'Maintenant',
        lastLoginLabel: 'Dernière connexion'
    },
    longRunningTaskBanners: {
        duplicateJob: {
            processing: {
                title: 'Duplication de \'${jobDescription}\' ${progress}% terminée...',
                content: {
                    message: 'Nous vous informerons dès que nous serons prêts.',
                    progressSeparator: 'de'
                }
            },
            completed: {
                title: 'Votre ${jobSingularLowerAlias}est prêt',
                content: {
                    message: 'Duplication de \'${jobDescription}\' réussie.'
                }
            },
            failed: {
                title: 'Échec de la duplication ${jobSingularLowerAlias}',
                content: {
                    message: 'Duplication de \'${jobDescription}\' échouée.',
                    retry: 'Réessayer'
                }
            },
            queued: {
                title: 'Votre duplicata de ${jobSingularLowerAlias} a été mis en file d\'attente',
                content: {
                    message: 'Nous vous informerons dès que nous serons prêts.',
                    progressSeparator: 'de'
                }
            },
            multiple: {
                title: 'Votre duplication de ${jobSingularLowerAlias} a été mise en file d\'attente',
                content: {
                    message: 'De nombreuses opérations se trouvent dans la file d\'attente.',
                    button: 'Afficher le journal d\'opération'
                }
            }
        }
    },
    longRunningjobIndicatorTooltipMessage: 'Une opération de longue haleine est actuellement en cours pour ce ${entityAlias}.',
    cMeProfiling: {
        aboutcMeColours: 'About C-me colours',
        about: 'FR_About_FR',
        cMeColourProfilingDialog: {
            dialogTitle: 'About C-me colour profiling',
            topMessageLine1: 'Human behaviours can be complicated to describe. We\'ve partnered with C-me, a behaviour profiling service that associates behaviours with colours. A resource\'s C-me data tells you about their preferred ways of doing things, expressed in the language of four colours.',
            topMessageLine2: 'We automatically update a resource\'s skills with traits from their most dominant colour.',
            redBox: {
                title: 'Red',
                boxList: [
                    'Action oriented',
                    'Assertive',
                    'Competitive',
                    'Decisive',
                    'Determined',
                    'Fast paced',
                    'Strategic'
                ]

            },
            yellowBox: {
                title: 'Yellow',
                boxList: [
                    'Dynamic presenter',
                    'Energetic',
                    'Flexible',
                    'Imaginative',
                    'Inspirational',
                    'Optimistic',
                    'Spontaneous'
                ]
            },
            greenBox: {
                title: 'Green',
                boxList: [
                    'Collaborative',
                    'Democratic',
                    'Diplomatic',
                    'Empathetic',
                    'Non-judgemental',
                    'Patient',
                    'Values driven'
                ]
            },
            blueBox: {
                title: 'Blue',
                boxList: [
                    'Analytical',
                    'Disciplined',
                    'Methodical',
                    'Organised',
                    'Precise',
                    'Systematic',
                    'Thorough'
                ]
            }
        }
    },
    summaryPage: {
        personalGreeting: 'Bonjour',
        customiseButtonLabel: 'Personnaliser',
        doneButtonLabel: 'Terminé',
        arrangeWidgetsText: 'Cliquez et faites glisser les widgets pour les réorganiser.',
        welcomeGreeting: 'Bienvenue sur Retain',
        summaryDurationOptionLabels: {
            4: 'Prochaines 4 semaines',
            6: 'Prochaines 6 semaines',
            12: 'Prochaines 12 semaines'
        },
        widgets: {
            yourRequests: {
                title: 'Vos demandes',
                emptyStateMessage: 'Aucune demande soumise par vous pour cette période'
            },
            plannedHours: {
                title: 'Heures prévues',
                emptyStateMessage: ''
            },
            ongoingJobs: {
                title: 'En cours ${jobPluralLowerAlias}',
                emptyStateMessage: ''
            },
            actionRequired: {
                title: '${rolerequestPluralCapitalAlias} à traiter',
                emptyStateMessage: 'Aucun ${rolerequestPluralLowerAlias} à traiter pour cette période'
            },
            jobsOverBudgetDetails: {
                title: '${jobPluralCapitalAlias} dépassement de budget',
                emptyStateMessage: 'Aucun ${jobPluralLowerAlias} en cours dépassant le budget'
            },
            chargeableUtilisation: {
                title: 'Utilisation payante',
                emptyStateMessage: ''
            },
            utilisation: {
                title: 'Utilisation',
                emptyStateMessage: ''
            },
            pendingRequests: {
                title: '${rolerequestPluralCapitalAlias} à traiter',
                subTitleUnit: 'heures',
                emptyStateMessage: ''
            },
            unassignedBookingsDetails: {
                title: 'Non attribué ${bookingPluralLowerAlias}',
                emptyStateMessage: 'Aucun élément non attribué ${bookingPluralLowerAlias} à traiter pour cette période'
            },
            unassignedBookings: {
                title: 'Non attribué ${bookingPluralLowerAlias}',
                subTitleUnit: '${jobPluralLowerAlias}',
                emptyStateMessage: ''
            },
            upcomingBookingsDetails: {
                title: 'Votre prochain ${bookingPluralLowerAlias}',
                emptyStateMessage: 'Aucun ${bookingPluralLowerAlias} attribué pour vous pour cette période'
            }
        },
        widgetDetailsTotalsUnit: {
            bookings: '${bookingPluralLowerAlias}',
            jobs: '${jobPluralLowerAlias}',
            hours: 'heures',
            planned: 'planifié',
            unconfirmed: 'non confirmé',
            availability: 'disponibilité',
            requests: 'demandes',
            rejected: 'Rejeté',
            requested: 'Demandé',
            draft: 'Brouillon',
            live: 'Actif'
        },
        budgetConsumedText: 'Budget consommé',
        pageTitle: 'Résumé',
        configurationPane: {
            arrangeButtonLabel: 'Organiser',
            searchPlaceholder: 'Rechercher',
            openOnLoginLabel: 'Ouvrir cette page lors de la connexion',
            emptySearchMessage: 'Aucun résultat trouvé.',
            sectionTitles: {
                personal: 'Personnel',
                bookings: '${bookingPluralCapitalAlias}',
                jobs: '${jobPluralCapitalAlias}',
                resources: '${resourcePluralCapitalAlias}',
                roles: '${rolerequestPluralCapitalAlias}'
            }
        },
        explainSummaryPageTextSecurity: 'Choisir les widgets qu\'ils peuvent ajouter à leur page de résumé. Les widgets personnels sont accessibles à tous les utilisateurs. \n\nLes widgets n\'afficheront pas les enregistrements et les champs masqués pour ce profil de sécurité.'
    },
    listPage: {
        pageTitle: 'FR_Lists_FR',
        workspacesMessages: {
            defaultWorkspaceLabel: 'FR_Default workspace_FR',
            newWorkspaceLabel: 'FR_New workspace_FR',
            saveChangesToPublicLabel: 'FR_Save changes to public_FR',
            saveAsNewWorkspaceLabel: 'FR_Save as a new workspace_FR',
            manageMyWorkspacesLabel: 'FR_Manage my workspaces_FR',
            privateWorkspacesLabel: 'FR_Private workspaces_FR',
            publicWorkspacesLabel: 'FR_Public workspaces_FR',
            noPublicWorkspacesCreatedLabel: 'FR_No public workspaces have been created_FR',
            noPrivateWorkspacesCreatedLabel: 'FR_No private workspaces have been created_FR'
        }
    }
};
