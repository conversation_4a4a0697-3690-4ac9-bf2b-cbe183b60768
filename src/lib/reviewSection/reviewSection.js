import React from 'react';
import PropTypes from 'prop-types';
import { Typography } from 'antd';
import './reviewSection.less';

/**
 * Content for the Reviews section.
 *
 * @param {Object} props
 * @param {number} props.reviewCount
 * @param {Function} props.onSubmitReview
 */
const ReviewSection = ({
    reviewCount,
    onSubmitReview
}) => (
    <div className="review-section">
        <div className="review-section__row">
            <Typography.Text className="ant-col ant-col-8 review-section__label">
                Skills reviews
            </Typography.Text>

            <div className="ant-col ant-col-12">
                <Typography.Text className="review-section__count">
                    {reviewCount} resources reviewed
                </Typography.Text>
                <br />
                <div>
                    <Typography.Link
                        className="review-section__submit"
                        onClick={onSubmitReview}
                    >
                        Submit skills review
                    </Typography.Link>
                </div>
            </div>
        </div>
    </div>

);

ReviewSection.propTypes = {
    reviewCount: PropTypes.number.isRequired,
    onSubmitReview: PropTypes.func.isRequired
};

export default ReviewSection;
