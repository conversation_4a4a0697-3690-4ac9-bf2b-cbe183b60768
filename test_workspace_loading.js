// Simple test to verify workspace loading functionality
const { buildStructure } = require('./src/state/skillStructure/mapCollection');

// Mock workspace data similar to what the API would return
const mockWorkspaceStructures = [
    {
        workspace_guid: 'workspace-1',
        workspace_description: 'Test Workspace 1',
        workspace_accesstype: 'private',
        workspace_editrights: 'edit'
    },
    {
        workspace_guid: 'workspace-2', 
        workspace_description: 'Test Workspace 2',
        workspace_accesstype: 'public',
        workspace_editrights: 'readonly'
    }
];

const mockMostRecentlyUsedWorkspaces = [
    {
        workspace_guid: 'workspace-1'
    }
];

// Test the buildStructure function
console.log('Testing buildStructure function...');
try {
    const result = buildStructure("workspace_guid", mockWorkspaceStructures);
    console.log('buildStructure result:', JSON.stringify(result, null, 2));
    console.log('Test passed!');
} catch (error) {
    console.error('Test failed:', error.message);
}

// Test with empty array
console.log('\nTesting with empty array...');
try {
    const result = buildStructure("workspace_guid", []);
    console.log('buildStructure result with empty array:', JSON.stringify(result, null, 2));
    console.log('Empty array test passed!');
} catch (error) {
    console.error('Empty array test failed:', error.message);
}

// Test with null/undefined
console.log('\nTesting with null...');
try {
    const result = buildStructure("workspace_guid", null);
    console.log('buildStructure result with null:', JSON.stringify(result, null, 2));
    console.log('Null test passed!');
} catch (error) {
    console.error('Null test failed:', error.message);
}
