import React from 'react';
import { shallow } from 'enzyme';
import { shallowToJson } from 'enzyme-to-json';
import { ConnectedManageMyWorkspacesWindow } from '../../src/connectedComponents/connectedManageMyWorkspacesWindow';
import ManagePlansWindow from '../../src/lib/managePlansWindow';
import { WORKSPACE_ACCESS_TYPES, WORKSPACE_EDIT_RIGHTS } from '../../src/constants';

// Mock the connected component to test the underlying component
jest.mock('react-redux', () => ({
    connect: (mapStateToProps, mapDispatchToProps, mergeProps) => (Component) => {
        // Return a component that can be tested
        const ConnectedComponent = (props) => {
            const stateProps = mapStateToProps ? mapStateToProps(props.mockState || {}) : {};
            const dispatchProps = mapDispatchToProps ? mapDispatchToProps(props.mockDispatch || jest.fn()) : {};
            const mergedProps = mergeProps ? mergeProps(stateProps, dispatchProps, props) : { ...stateProps, ...dispatchProps, ...props };
            return <Component {...mergedProps} />;
        };
        ConnectedComponent.displayName = `Connected(${Component.displayName || Component.name})`;
        return ConnectedComponent;
    }
}));

describe('ConnectedManageMyWorkspacesWindow', () => {
    const mockState = {
        listPage: {
            workspaces: {
                selected: 'workspace-guid-1',
                workspacesStructure: {
                    map: {
                        'workspace-guid-1': {
                            workspace_guid: 'workspace-guid-1',
                            workspace_description: 'Test Workspace 1',
                            workspace_accesstype: 'private',
                            workspace_editrights: 'edit'
                        },
                        'workspace-guid-2': {
                            workspace_guid: 'workspace-guid-2',
                            workspace_description: 'Test Workspace 2',
                            workspace_accesstype: 'public',
                            workspace_editrights: 'readonly'
                        }
                    },
                    orderedKeys: ['workspace-guid-1', 'workspace-guid-2']
                },
                workspacesSettings: {
                    map: {
                        'workspace-guid-1': { workspace_guid: 'workspace-guid-1' },
                        'workspace-guid-2': { workspace_guid: 'workspace-guid-2' }
                    }
                },
                workspacesStructureChanges: {
                    inserts: { map: {} }
                }
            }
        },
        plannerPage: {
            manageMyPlans: {
                visible: true,
                workspacesInEditMode: {},
                managePublicPlansFNAName: 'ManageAllPublicPlans'
            }
        },
        applicationUser: {
            userId: 'user-123'
        },
        applicationFnas: {
            fnas: ['ManageAllPublicPlans']
        },
        featureManagement: {
            featureFlags: {
                LIST_PAGE_AND_BULK_UPDATE: true
            }
        },
        internationalization: {
            translations: {
                managePlansWindow: {
                    newPlanLabel: 'New Plan',
                    plansColumnMessages: {
                        privatePlansColumnTitle: 'My workspaces',
                        publicPlansColumnTitle: 'Public workspaces'
                    }
                }
            }
        }
    };

    const mockDispatch = jest.fn();

    const defaultProps = {
        mockState,
        mockDispatch
    };

    it('should render without crashing', () => {
        const wrapper = shallow(<ConnectedManageMyWorkspacesWindow {...defaultProps} />);
        expect(wrapper).toBeDefined();
    });

    it('should pass correct props to ManagePlansWindow', () => {
        const wrapper = shallow(<ConnectedManageMyWorkspacesWindow {...defaultProps} />);
        const managePlansWindow = wrapper.find(ManagePlansWindow);
        
        expect(managePlansWindow).toHaveLength(1);
        
        const props = managePlansWindow.props();
        expect(props.visible).toBe(true);
        expect(props.privatePlansColumn).toBeDefined();
        expect(props.publicPlansColumn).toBeDefined();
        expect(props.actions).toBeDefined();
        expect(props.promptActions).toBeDefined();
    });

    it('should map listPage.workspaces state correctly', () => {
        const wrapper = shallow(<ConnectedManageMyWorkspacesWindow {...defaultProps} />);
        const managePlansWindow = wrapper.find(ManagePlansWindow);
        const props = managePlansWindow.props();

        // Should have private and public workspaces
        expect(props.privatePlansColumn.plans).toBeDefined();
        expect(props.publicPlansColumn.plans).toBeDefined();
        
        // Should include the test workspaces
        const allPlans = [...props.privatePlansColumn.plans, ...props.publicPlansColumn.plans];
        const testWorkspace1 = allPlans.find(plan => plan.guid === 'workspace-guid-1');
        const testWorkspace2 = allPlans.find(plan => plan.guid === 'workspace-guid-2');
        
        expect(testWorkspace1).toBeDefined();
        expect(testWorkspace1.description).toBe('Test Workspace 1');
        expect(testWorkspace2).toBeDefined();
        expect(testWorkspace2.description).toBe('Test Workspace 2');
    });

    it('should provide workspace action functions', () => {
        const wrapper = shallow(<ConnectedManageMyWorkspacesWindow {...defaultProps} />);
        const managePlansWindow = wrapper.find(ManagePlansWindow);
        const props = managePlansWindow.props();

        expect(props.actions.copyPlan).toBeInstanceOf(Function);
        expect(props.actions.digestCreatePlan).toBeInstanceOf(Function);
        expect(props.promptActions.promptDeletePlan).toBeInstanceOf(Function);
        expect(props.promptActions.promptRenamePlan).toBeInstanceOf(Function);
        expect(props.promptActions.promptSelectPlan).toBeInstanceOf(Function);
        expect(props.promptActions.promptMovePlan).toBeInstanceOf(Function);
    });

    it('should handle workspace actions correctly', () => {
        const wrapper = shallow(<ConnectedManageMyWorkspacesWindow {...defaultProps} />);
        const managePlansWindow = wrapper.find(ManagePlansWindow);
        const props = managePlansWindow.props();

        // Test delete action
        props.promptActions.promptDeletePlan('test-workspace-guid');
        expect(mockDispatch).toHaveBeenCalled();

        // Test rename action
        props.promptActions.promptRenamePlan('test-workspace-guid', 'New Name');
        expect(mockDispatch).toHaveBeenCalled();

        // Test copy action
        props.actions.copyPlan('template-guid', WORKSPACE_ACCESS_TYPES.PRIVATE, WORKSPACE_EDIT_RIGHTS.EDIT, 'Original Name');
        expect(mockDispatch).toHaveBeenCalled();
    });
});
